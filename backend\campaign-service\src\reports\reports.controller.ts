import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Param,
  UseGuards,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { Response } from 'express';
import { 
  ReportsService, 
  ReportFilter, 
  ExportOptions 
} from './reports.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';

export class GenerateReportDto {
  filters: ReportFilter;
  exportOptions?: ExportOptions;
}

@ApiTags('Reports')
@Controller('reports')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  @Post('campaign-performance')
  @ApiOperation({ summary: 'Gerar relatório de performance de campanhas' })
  @ApiResponse({ 
    status: 200, 
    description: 'Relatório de performance de campanhas',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            totalCampaigns: { type: 'number' },
            activeCampaigns: { type: 'number' },
            completedCampaigns: { type: 'number' },
            totalReach: { type: 'number' },
            totalEngagement: { type: 'number' },
            totalConversions: { type: 'number' },
            totalRevenue: { type: 'number' },
            averageROI: { type: 'number' },
            averageIncentive: { type: 'number' }
          }
        },
        campaigns: { type: 'array', items: { type: 'object' } },
        trends: { type: 'object' },
        topPerformers: { type: 'object' }
      }
    }
  })
  async generateCampaignPerformanceReport(@Body() dto: GenerateReportDto) {
    return this.reportsService.generateCampaignPerformanceReport(dto.filters);
  }

  @Post('product-performance')
  @ApiOperation({ summary: 'Gerar relatório de performance de produtos' })
  @ApiResponse({ 
    status: 200, 
    description: 'Relatório de performance de produtos',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            totalProducts: { type: 'number' },
            activeProducts: { type: 'number' },
            totalSales: { type: 'number' },
            totalRevenue: { type: 'number' },
            averageRating: { type: 'number' },
            averagePrice: { type: 'number' }
          }
        },
        products: { type: 'array', items: { type: 'object' } },
        categories: { type: 'array', items: { type: 'object' } },
        insights: { type: 'object' }
      }
    }
  })
  async generateProductPerformanceReport(@Body() dto: GenerateReportDto) {
    return this.reportsService.generateProductPerformanceReport(dto.filters);
  }

  @Post('incentive-analysis')
  @ApiOperation({ summary: 'Gerar relatório de análise de incentivos' })
  @ApiResponse({ 
    status: 200, 
    description: 'Relatório de análise de incentivos',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            totalIncentives: { type: 'number' },
            totalIncentiveValue: { type: 'number' },
            averageIncentivePercentage: { type: 'number' },
            totalSavings: { type: 'number' },
            conversionRate: { type: 'number' },
            roi: { type: 'number' }
          }
        },
        incentiveRanges: { type: 'array', items: { type: 'object' } },
        productAnalysis: { type: 'array', items: { type: 'object' } },
        trends: { type: 'object' },
        recommendations: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async generateIncentiveAnalysisReport(@Body() dto: GenerateReportDto) {
    return this.reportsService.generateIncentiveAnalysisReport(dto.filters);
  }

  @Post('user-activity')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Gerar relatório de atividade de usuários (apenas admins)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Relatório de atividade de usuários',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            totalUsers: { type: 'number' },
            activeUsers: { type: 'number' },
            totalSessions: { type: 'number' },
            averageSessionDuration: { type: 'number' },
            totalActions: { type: 'number' }
          }
        },
        userTypes: { type: 'array', items: { type: 'object' } },
        activity: { type: 'object' },
        topUsers: { type: 'array', items: { type: 'object' } }
      }
    }
  })
  async generateUserActivityReport(@Body() dto: GenerateReportDto) {
    return this.reportsService.generateUserActivityReport(dto.filters);
  }

  @Post('export')
  @ApiOperation({ summary: 'Exportar relatório em formato específico' })
  @ApiResponse({ 
    status: 200, 
    description: 'Informações do arquivo exportado',
    schema: {
      type: 'object',
      properties: {
        downloadUrl: { type: 'string' },
        filename: { type: 'string' },
        size: { type: 'number' }
      }
    }
  })
  async exportReport(@Body() exportData: {
    reportType: 'campaign' | 'product' | 'incentive' | 'user';
    filters: ReportFilter;
    options: ExportOptions;
  }) {
    return this.reportsService.exportReport(
      exportData.reportType,
      exportData.filters,
      exportData.options,
    );
  }

  @Get('templates')
  @ApiOperation({ summary: 'Obter templates de relatórios predefinidos' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de templates de relatórios',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          description: { type: 'string' },
          type: { type: 'string' },
          filters: { type: 'object' },
          schedule: { type: 'string' }
        }
      }
    }
  })
  async getReportTemplates() {
    return this.reportsService.getReportTemplates();
  }

  @Get('dashboard')
  @ApiOperation({ summary: 'Obter dados completos do dashboard' })
  @ApiQuery({ name: 'industryId', required: false, description: 'Filtrar por indústria' })
  @ApiResponse({
    status: 200,
    description: 'Dados completos do dashboard',
    schema: {
      type: 'object',
      properties: {
        totalCampaigns: { type: 'number' },
        activeCampaigns: { type: 'number' },
        totalInvestment: { type: 'number' },
        totalRevenue: { type: 'number' },
        impactedConsumers: { type: 'number' },
        conversionRate: { type: 'number' }
      }
    }
  })
  async getDashboardData(
    @Query('industryId') industryId?: string,
  ) {
    // Mock data for demonstration
    // In production, this would query actual database
    return {
      totalCampaigns: Math.floor(Math.random() * 50) + 20,
      activeCampaigns: Math.floor(Math.random() * 15) + 5,
      totalInvestment: Math.floor(Math.random() * 500000) + 100000,
      totalRevenue: Math.floor(Math.random() * 2000000) + 500000,
      impactedConsumers: Math.floor(Math.random() * 50000) + 10000,
      conversionRate: parseFloat((Math.random() * 20 + 5).toFixed(2)),
    };
  }

  @Get('monthly-totals')
  @ApiOperation({ summary: 'Obter totais mensais por indústria' })
  @ApiQuery({ name: 'month', required: false, description: 'Mês no formato YYYY-MM' })
  @ApiResponse({
    status: 200,
    description: 'Totais mensais por indústria',
    schema: {
      type: 'object',
      properties: {
        month: { type: 'string' },
        totals: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              industryId: { type: 'string' },
              industryName: { type: 'string' },
              totalDiscounts: { type: 'number' },
              totalCampaigns: { type: 'number' },
              activeCampaigns: { type: 'number' },
              growth: { type: 'number' },
              lastUpdate: { type: 'string' }
            }
          }
        }
      }
    }
  })
  async getMonthlyTotals(
    @Query('month') month?: string,
  ) {
    const currentMonth = month || new Date().toISOString().slice(0, 7);

    // Mock data for demonstration
    const mockTotals = [
      {
        industryId: 'ind-001',
        industryName: 'Unilever Brasil',
        totalDiscounts: Math.floor(Math.random() * 200000) + 50000,
        totalCampaigns: Math.floor(Math.random() * 20) + 5,
        activeCampaigns: Math.floor(Math.random() * 10) + 2,
        growth: parseFloat((Math.random() * 40 - 10).toFixed(1)),
        lastUpdate: new Date().toISOString()
      },
      {
        industryId: 'ind-002',
        industryName: 'Nestlé Brasil',
        totalDiscounts: Math.floor(Math.random() * 150000) + 40000,
        totalCampaigns: Math.floor(Math.random() * 15) + 3,
        activeCampaigns: Math.floor(Math.random() * 8) + 1,
        growth: parseFloat((Math.random() * 40 - 10).toFixed(1)),
        lastUpdate: new Date().toISOString()
      },
      {
        industryId: 'ind-003',
        industryName: 'P&G Brasil',
        totalDiscounts: Math.floor(Math.random() * 250000) + 60000,
        totalCampaigns: Math.floor(Math.random() * 25) + 8,
        activeCampaigns: Math.floor(Math.random() * 12) + 3,
        growth: parseFloat((Math.random() * 40 - 10).toFixed(1)),
        lastUpdate: new Date().toISOString()
      }
    ];

    return {
      month: currentMonth,
      totals: mockTotals
    };
  }

  @Get('monthly-totals/:industryId/campaigns')
  @ApiOperation({ summary: 'Obter campanhas detalhadas de uma indústria no mês' })
  @ApiQuery({ name: 'month', required: false, description: 'Mês no formato YYYY-MM' })
  @ApiResponse({
    status: 200,
    description: 'Campanhas detalhadas da indústria',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            totalCampaigns: { type: 'number' },
            totalDiscounts: { type: 'number' },
            averageDiscount: { type: 'number' }
          }
        },
        campaigns: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              totalDiscounts: { type: 'number' },
              startDate: { type: 'string' },
              endDate: { type: 'string' },
              status: { type: 'string' },
              productsCount: { type: 'number' }
            }
          }
        }
      }
    }
  })
  async getIndustryMonthlyCampaigns(
    @Param('industryId') industryId: string,
    @Query('month') month?: string,
  ) {
    // Mock drill-down data
    const mockCampaigns = [
      {
        id: 'camp-001',
        name: 'Promoção Produtos de Limpeza',
        totalDiscounts: Math.floor(Math.random() * 50000) + 10000,
        startDate: '2025-09-01',
        endDate: '2025-09-15',
        status: 'completed',
        productsCount: Math.floor(Math.random() * 5) + 1
      },
      {
        id: 'camp-002',
        name: 'Desconto Produtos de Higiene',
        totalDiscounts: Math.floor(Math.random() * 40000) + 8000,
        startDate: '2025-09-10',
        endDate: '2025-09-30',
        status: 'active',
        productsCount: Math.floor(Math.random() * 4) + 1
      },
      {
        id: 'camp-003',
        name: 'Oferta Produtos de Beleza',
        totalDiscounts: Math.floor(Math.random() * 60000) + 15000,
        startDate: '2025-09-05',
        endDate: '2025-09-25',
        status: 'active',
        productsCount: Math.floor(Math.random() * 6) + 2
      }
    ];

    const totalDiscounts = mockCampaigns.reduce((sum, camp) => sum + camp.totalDiscounts, 0);
    const averageDiscount = totalDiscounts / mockCampaigns.length;

    return {
      summary: {
        totalCampaigns: mockCampaigns.length,
        totalDiscounts,
        averageDiscount
      },
      campaigns: mockCampaigns
    };
  }

  @Get('quick-stats')
  @ApiOperation({ summary: 'Obter estatísticas rápidas do dashboard' })
  @ApiQuery({ name: 'industryId', required: false, description: 'Filtrar por indústria' })
  @ApiResponse({ 
    status: 200, 
    description: 'Estatísticas rápidas',
    schema: {
      type: 'object',
      properties: {
        campaigns: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            active: { type: 'number' },
            completed: { type: 'number' },
            totalReach: { type: 'number' }
          }
        },
        products: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            active: { type: 'number' },
            totalSales: { type: 'number' },
            totalRevenue: { type: 'number' }
          }
        },
        incentives: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            totalValue: { type: 'number' },
            averageROI: { type: 'number' }
          }
        },
        trends: {
          type: 'object',
          properties: {
            revenueGrowth: { type: 'number' },
            campaignGrowth: { type: 'number' },
            conversionGrowth: { type: 'number' }
          }
        }
      }
    }
  })
  async getQuickStats(@Query('industryId') industryId?: string) {
    const filters: ReportFilter = {
      industryId,
      dateRange: {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date().toISOString(),
      },
    };

    const [campaignReport, productReport, incentiveReport] = await Promise.all([
      this.reportsService.generateCampaignPerformanceReport(filters),
      this.reportsService.generateProductPerformanceReport(filters),
      this.reportsService.generateIncentiveAnalysisReport(filters),
    ]);

    return {
      campaigns: {
        total: campaignReport.summary.totalCampaigns,
        active: campaignReport.summary.activeCampaigns,
        completed: campaignReport.summary.completedCampaigns,
        totalReach: campaignReport.summary.totalReach,
      },
      products: {
        total: productReport.summary.totalProducts,
        active: productReport.summary.activeProducts,
        totalSales: productReport.summary.totalSales,
        totalRevenue: productReport.summary.totalRevenue,
      },
      incentives: {
        total: incentiveReport.summary.totalIncentives,
        totalValue: incentiveReport.summary.totalIncentiveValue,
        averageROI: incentiveReport.summary.roi,
      },
      trends: {
        revenueGrowth: Math.floor(Math.random() * 20) + 5, // Mock growth percentage
        campaignGrowth: Math.floor(Math.random() * 15) + 3,
        conversionGrowth: Math.floor(Math.random() * 25) + 8,
      },
    };
  }

  @Get('dashboard-charts')
  @ApiOperation({ summary: 'Obter dados para gráficos do dashboard' })
  @ApiQuery({ name: 'industryId', required: false, description: 'Filtrar por indústria' })
  @ApiQuery({ name: 'period', required: false, enum: ['7d', '30d', '90d', '1y'], description: 'Período dos dados' })
  @ApiResponse({ 
    status: 200, 
    description: 'Dados para gráficos',
    schema: {
      type: 'object',
      properties: {
        revenueChart: {
          type: 'object',
          properties: {
            labels: { type: 'array', items: { type: 'string' } },
            datasets: { type: 'array', items: { type: 'object' } }
          }
        },
        campaignChart: { type: 'object' },
        conversionChart: { type: 'object' },
        categoryChart: { type: 'object' }
      }
    }
  })
  async getDashboardCharts(
    @Query('industryId') industryId?: string,
    @Query('period') period: '7d' | '30d' | '90d' | '1y' = '30d',
  ) {
    const days = period === '7d' ? 7 : period === '30d' ? 30 : period === '90d' ? 90 : 365;
    
    const filters: ReportFilter = {
      industryId,
      dateRange: {
        startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date().toISOString(),
      },
    };

    const campaignReport = await this.reportsService.generateCampaignPerformanceReport(filters);
    
    // Generate chart data
    const labels = campaignReport.trends.daily.slice(0, days).map(d => d.date);
    
    return {
      revenueChart: {
        labels,
        datasets: [
          {
            label: 'Receita',
            data: campaignReport.trends.daily.slice(0, days).map(d => d.revenue),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
          },
        ],
      },
      campaignChart: {
        labels: ['Ativas', 'Concluídas', 'Pausadas'],
        datasets: [
          {
            data: [
              campaignReport.summary.activeCampaigns,
              campaignReport.summary.completedCampaigns,
              Math.floor(Math.random() * 5) + 1, // Paused campaigns
            ],
            backgroundColor: ['#10B981', '#3B82F6', '#F59E0B'],
          },
        ],
      },
      conversionChart: {
        labels,
        datasets: [
          {
            label: 'Conversões',
            data: campaignReport.trends.daily.slice(0, days).map(d => d.conversions),
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
          },
        ],
      },
      categoryChart: {
        labels: campaignReport.topPerformers.categories.map(c => c.name),
        datasets: [
          {
            label: 'Receita por Categoria',
            data: campaignReport.topPerformers.categories.map(c => c.revenue),
            backgroundColor: [
              '#8B5CF6',
              '#06B6D4',
              '#84CC16',
              '#F97316',
              '#EF4444',
            ],
          },
        ],
      },
    };
  }

  @Get('download/:filename')
  @ApiOperation({ summary: 'Download de arquivo de relatório exportado' })
  @ApiResponse({ status: 200, description: 'Arquivo de relatório' })
  async downloadReport(@Param('filename') filename: string, @Res() res: Response) {
    // In a real implementation, this would:
    // 1. Validate the filename and user permissions
    // 2. Retrieve the file from storage
    // 3. Stream the file to the response
    
    // For now, return a mock response
    res.set({
      'Content-Type': 'application/octet-stream',
      'Content-Disposition': `attachment; filename="${filename}"`,
    });
    
    res.send('Mock report file content');
  }

  @Post('schedule')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Agendar geração automática de relatório' })
  @ApiResponse({ status: 200, description: 'Relatório agendado com sucesso' })
  async scheduleReport(@Body() scheduleData: {
    templateId: string;
    schedule: string; // cron expression
    recipients: string[];
    format: 'csv' | 'xlsx' | 'pdf';
  }) {
    // In a real implementation, this would:
    // 1. Validate the schedule expression
    // 2. Store the schedule in database
    // 3. Set up cron job or queue job
    
    return {
      message: 'Relatório agendado com sucesso',
      scheduleId: `schedule_${Date.now()}`,
      nextRun: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      ...scheduleData,
    };
  }

  @Get('scheduled')
  @UseGuards(RolesGuard)
  @Roles('admin', 'editor')
  @ApiOperation({ summary: 'Listar relatórios agendados' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de relatórios agendados',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          templateId: { type: 'string' },
          templateName: { type: 'string' },
          schedule: { type: 'string' },
          recipients: { type: 'array', items: { type: 'string' } },
          format: { type: 'string' },
          isActive: { type: 'boolean' },
          lastRun: { type: 'string', format: 'date-time' },
          nextRun: { type: 'string', format: 'date-time' },
          createdAt: { type: 'string', format: 'date-time' }
        }
      }
    }
  })
  async getScheduledReports() {
    // Mock scheduled reports
    return [
      {
        id: 'schedule_1',
        templateId: 'weekly-campaign-summary',
        templateName: 'Resumo Semanal de Campanhas',
        schedule: '0 9 * * 1', // Every Monday at 9 AM
        recipients: ['<EMAIL>', '<EMAIL>'],
        format: 'xlsx',
        isActive: true,
        lastRun: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        nextRun: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: 'schedule_2',
        templateId: 'monthly-product-analysis',
        templateName: 'Análise Mensal de Produtos',
        schedule: '0 8 1 * *', // First day of month at 8 AM
        recipients: ['<EMAIL>'],
        format: 'pdf',
        isActive: true,
        lastRun: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        nextRun: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
      },
    ];
  }
}
