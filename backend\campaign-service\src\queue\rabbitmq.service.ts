import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as amqp from 'amqplib';

/**
 * Serviço RabbitMQ para Campaign Service
 * - Processamento de campanhas
 * - Geração de relatórios
 * - Sincronização com sistemas externos
 * - Notificações de campanha
 */
@Injectable()
export class RabbitMQService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RabbitMQService.name);
  private connection: any = null;
  private channel: any = null;
  private readonly exchanges = {
    CAMPAIGNS: 'campaigns.exchange',
    REPORTS: 'reports.exchange',
    INTEGRATIONS: 'integrations.exchange',
    NOTIFICATIONS: 'notifications.exchange',
  };
  private readonly queues = {
    CAMPAIGN_CREATE: 'campaign.create',
    CAMPAIGN_UPDATE: 'campaign.update',
    CAMPAIGN_ACTIVATE: 'campaign.activate',
    CAMPAIGN_DEACTIVATE: 'campaign.deactivate',
    REPORT_PERFORMANCE: 'report.performance',
    REPORT_ATTRIBUTION: 'report.attribution',
    INTEGRATION_VTEX: 'integration.vtex',
    INTEGRATION_ERP: 'integration.erp',
    NOTIFICATION_CAMPAIGN: 'notification.campaign',
  };

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    await this.connect();
    await this.setupExchangesAndQueues();
    this.logger.log('Campaign RabbitMQ service initialized successfully');
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  private async connect(): Promise<void> {
    try {
      const rabbitmqUrl = this.configService.get('RABBITMQ_URL', 'amqp://rabbitmq:rabbitmq123@rabbitmq:5672');
      this.connection = await amqp.connect(rabbitmqUrl);
      this.channel = await this.connection.createChannel();
      
      await this.channel.prefetch(10);
      
      this.logger.log('Connected to RabbitMQ successfully');
    } catch (error) {
      this.logger.error('Failed to connect to RabbitMQ:', error);
      throw error;
    }
  }

  private async disconnect(): Promise<void> {
    try {
      if (this.channel) {
        await this.channel.close();
        this.channel = null;
      }
      if (this.connection) {
        await this.connection.close();
        this.connection = null;
      }
      this.logger.log('Disconnected from RabbitMQ');
    } catch (error) {
      this.logger.error('Error disconnecting from RabbitMQ:', error);
    }
  }

  private async setupExchangesAndQueues(): Promise<void> {
    try {
      if (!this.channel) {
        throw new Error('Channel not available');
      }

      // Setup exchanges
      for (const exchange of Object.values(this.exchanges)) {
        await this.channel.assertExchange(exchange, 'topic', { durable: true });
      }

      // Setup campaign queues
      await this.setupCampaignQueues();
      await this.setupReportQueues();
      await this.setupIntegrationQueues();
      await this.setupNotificationQueues();

      this.logger.log('All campaign exchanges and queues setup successfully');
    } catch (error) {
      this.logger.error('Failed to setup exchanges and queues:', error);
      throw error;
    }
  }

  private async setupCampaignQueues(): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not available');
    }

    // Campaign creation queue
    await this.channel.assertQueue(this.queues.CAMPAIGN_CREATE, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.campaigns',
        'x-message-ttl': 600000, // 10 minutes TTL
      }
    });
    await this.channel.bindQueue(this.queues.CAMPAIGN_CREATE, this.exchanges.CAMPAIGNS, 'campaign.create.*');

    // Campaign update queue
    await this.channel.assertQueue(this.queues.CAMPAIGN_UPDATE, { 
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.campaigns',
      }
    });
    await this.channel.bindQueue(this.queues.CAMPAIGN_UPDATE, this.exchanges.CAMPAIGNS, 'campaign.update.*');

    // Campaign activation queue
    await this.channel.assertQueue(this.queues.CAMPAIGN_ACTIVATE, { 
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.campaigns',
        'x-max-priority': 8,
      }
    });
    await this.channel.bindQueue(this.queues.CAMPAIGN_ACTIVATE, this.exchanges.CAMPAIGNS, 'campaign.activate.*');

    // Campaign deactivation queue
    await this.channel.assertQueue(this.queues.CAMPAIGN_DEACTIVATE, { 
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.campaigns',
        'x-max-priority': 8,
      }
    });
    await this.channel.bindQueue(this.queues.CAMPAIGN_DEACTIVATE, this.exchanges.CAMPAIGNS, 'campaign.deactivate.*');
  }

  private async setupReportQueues(): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not available');
    }

    // Performance report queue
    await this.channel.assertQueue(this.queues.REPORT_PERFORMANCE, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.reports',
        'x-message-ttl': 1800000, // 30 minutes TTL
      }
    });
    await this.channel.bindQueue(this.queues.REPORT_PERFORMANCE, this.exchanges.REPORTS, 'report.performance.*');

    // Attribution report queue
    await this.channel.assertQueue(this.queues.REPORT_ATTRIBUTION, { 
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.reports',
        'x-message-ttl': 1800000, // 30 minutes TTL
      }
    });
    await this.channel.bindQueue(this.queues.REPORT_ATTRIBUTION, this.exchanges.REPORTS, 'report.attribution.*');
  }

  private async setupIntegrationQueues(): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not available');
    }

    // VTEX integration queue
    await this.channel.assertQueue(this.queues.INTEGRATION_VTEX, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.integrations',
        'x-message-ttl': 300000, // 5 minutes TTL
      }
    });
    await this.channel.bindQueue(this.queues.INTEGRATION_VTEX, this.exchanges.INTEGRATIONS, 'integration.vtex.*');

    // ERP integration queue
    await this.channel.assertQueue(this.queues.INTEGRATION_ERP, { 
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.integrations',
        'x-message-ttl': 600000, // 10 minutes TTL
      }
    });
    await this.channel.bindQueue(this.queues.INTEGRATION_ERP, this.exchanges.INTEGRATIONS, 'integration.erp.*');
  }

  private async setupNotificationQueues(): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not available');
    }

    // Campaign notifications
    await this.channel.assertQueue(this.queues.NOTIFICATION_CAMPAIGN, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.notifications',
      }
    });
    await this.channel.bindQueue(this.queues.NOTIFICATION_CAMPAIGN, this.exchanges.NOTIFICATIONS, 'notification.campaign.*');
  }

  /**
   * Publish message to queue
   */
  async publishMessage(exchange: string, routingKey: string, message: any, options: any = {}): Promise<boolean> {
    try {
      if (!this.channel) {
        throw new Error('Channel not available');
      }

      const messageBuffer = Buffer.from(JSON.stringify(message));
      const publishOptions = {
        persistent: true,
        timestamp: Date.now(),
        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        ...options,
      };

      const result = this.channel.publish(exchange, routingKey, messageBuffer, publishOptions);
      
      if (result) {
        this.logger.debug(`Message published to ${exchange}/${routingKey}`);
      } else {
        this.logger.warn(`Failed to publish message to ${exchange}/${routingKey}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error(`Error publishing message to ${exchange}/${routingKey}:`, error);
      return false;
    }
  }

  /**
   * Process campaign creation
   */
  async processCampaignCreation(campaignId: string, userId: string, campaignData: any): Promise<boolean> {
    const message = {
      campaignId,
      userId,
      campaignData,
      timestamp: new Date().toISOString(),
    };

    return this.publishMessage(
      this.exchanges.CAMPAIGNS,
      'campaign.create.process',
      message
    );
  }

  /**
   * Generate performance report
   */
  async generatePerformanceReport(campaignId: string, userId: string, dateRange: any): Promise<boolean> {
    const message = {
      campaignId,
      userId,
      dateRange,
      reportId: `perf-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
    };

    return this.publishMessage(
      this.exchanges.REPORTS,
      'report.performance.generate',
      message
    );
  }

  /**
   * Sync with VTEX
   */
  async syncWithVtex(operation: string, data: any): Promise<boolean> {
    const message = {
      operation,
      data,
      syncId: `vtex-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
    };

    return this.publishMessage(
      this.exchanges.INTEGRATIONS,
      `integration.vtex.${operation}`,
      message
    );
  }

  /**
   * Send campaign notification
   */
  async sendCampaignNotification(userId: string, campaignId: string, type: string, data: any): Promise<boolean> {
    const message = {
      userId,
      campaignId,
      type,
      data,
      timestamp: new Date().toISOString(),
    };

    return this.publishMessage(
      this.exchanges.NOTIFICATIONS,
      `notification.campaign.${type}`,
      message
    );
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; connection: boolean; channel: boolean }> {
    try {
      const connectionOk = !!this.connection;
      const channelOk = !!this.channel;

      return {
        status: connectionOk && channelOk ? 'healthy' : 'unhealthy',
        connection: connectionOk,
        channel: channelOk,
      };
    } catch (error) {
      this.logger.error('RabbitMQ health check failed:', error);
      return {
        status: 'unhealthy',
        connection: false,
        channel: false,
      };
    }
  }

  /**
   * Get queue stats
   */
  async getQueueStats(): Promise<any> {
    try {
      if (!this.channel) {
        throw new Error('Channel not available');
      }

      const stats = {};
      for (const [name, queue] of Object.entries(this.queues)) {
        try {
          const queueInfo = await this.channel.checkQueue(queue);
          stats[name] = {
            queue,
            messageCount: queueInfo.messageCount,
            consumerCount: queueInfo.consumerCount,
          };
        } catch (error) {
          stats[name] = { queue, error: error.message };
        }
      }
      return stats;
    } catch (error) {
      this.logger.error('Failed to get queue stats:', error);
      return { error: error.message };
    }
  }
}
