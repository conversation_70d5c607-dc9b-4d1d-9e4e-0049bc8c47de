import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { ThrottlerModule } from '@nestjs/throttler';
import { JwtModule } from '@nestjs/jwt';
import { ScheduleModule } from '@nestjs/schedule';
import * as redisStore from 'cache-manager-redis-store';

import { CampaignsModule } from './campaigns/campaigns.module';
import { IndustryRulesModule } from './industry-rules/industry-rules.module';
import { BusinessRulesModule } from './business-rules/business-rules.module';
import { ValidationModule } from './validation/validation.module';
import { IncentiveValidityModule } from './incentive-validity/incentive-validity.module';
import { ProductSelectionModule } from './product-selection/product-selection.module';
import { IncentiveCalculatorModule } from './incentive-calculator/incentive-calculator.module';
import { CycleManagementModule } from './cycle-management/cycle-management.module';
import { AIEstimatorModule } from './ai-estimator/ai-estimator.module';
import { SecurityModule } from './security/security.module';
import { ReportsModule } from './reports/reports.module';
import { PriceIntegrationModule } from './price-integration/price-integration.module';
import { AttributionModule } from './attribution/attribution.module';
import { AuditModule } from './audit/audit.module';
import { LgpdModule } from './lgpd/lgpd.module';
import { QueueModule } from './queue/queue.module';
import { DatabaseConfig } from './config/database.config';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useClass: DatabaseConfig,
    }),

    // Cache configuration (simplified)
    CacheModule.register({
      ttl: 300, // 5 minutes
      max: 100,
      isGlobal: true,
    }),

    // Rate limiting (simplified)
    ThrottlerModule.forRoot([{
      ttl: 60000, // 1 minute
      limit: 10,
    }]),

    // Schedule module for cron jobs
    ScheduleModule.forRoot(),

    // JWT
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '8h'),
        },
      }),
      inject: [ConfigService],
      global: true,
    }),

    // Feature modules
    CampaignsModule,
    IndustryRulesModule,
    BusinessRulesModule,
    ValidationModule,
    IncentiveValidityModule,
    ProductSelectionModule,
    IncentiveCalculatorModule,
    CycleManagementModule,
    AIEstimatorModule,
    SecurityModule,
    ReportsModule,
    PriceIntegrationModule,
    AttributionModule,
    AuditModule,
    LgpdModule,
    QueueModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
