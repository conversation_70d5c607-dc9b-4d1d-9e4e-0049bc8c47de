import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppMockModule } from './app-mock.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    const app = await NestFactory.create(AppMockModule);
    const configService = app.get(ConfigService);

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    // CORS
    app.enableCors({
      origin: configService.get('CORS_ORIGIN', 'http://localhost:3000'),
      credentials: true,
    });

    const port = configService.get('PORT', 3001);
    await app.listen(port);

    logger.log(`🚀 Auth Service (Mock) is running on: http://localhost:${port}`);
    logger.log(`📋 Health check: http://localhost:${port}/api/v1/health`);
    logger.log(`🔐 OTP Request: POST http://localhost:${port}/api/v1/auth/industries/request-otp`);
    logger.log(`🔑 Authentication: POST http://localhost:${port}/api/v1/auth/industries/authenticate`);
  } catch (error) {
    logger.error('❌ Error starting the application:', error);
    process.exit(1);
  }
}

bootstrap();
