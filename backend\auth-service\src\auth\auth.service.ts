import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
  NotFoundException,
  Logger,
  Inject,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';
import { v4 as uuidv4 } from 'uuid';

import { User, UserStatus, UserRole } from '../database/entities/user.entity';
import { Otp, OtpType, OtpStatus } from '../database/entities/otp.entity';
import { AuditLog, AuditAction, AuditLevel } from '../database/entities/audit-log.entity';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { RequestOtpDto } from './dto/request-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { AuthResponse } from './interfaces/auth-response.interface';
import { RedisCacheService } from '../cache/redis-cache.service';
// import { RabbitMQService } from '../queue/rabbitmq.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Otp)
    private readonly otpRepository: Repository<Otp>,
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    private readonly redisCacheService: RedisCacheService,
    // private readonly rabbitMQService: RabbitMQService,
  ) {}

  /**
   * Solicita OTP para indústria conforme T-001 do backlog
   * - Validação de e-mail corporativo (não gmail/hotmail)
   * - OTP de 6 dígitos numéricos
   * - Expiração de 5 minutos
   * - Rate limiting: máximo 3 OTPs por hora por e-mail
   * - Auditoria com timestamp e IP
   */
  async requestOtpForIndustry(requestOtpDto: RequestOtpDto, ip: string, userAgent: string): Promise<{ message: string; expiresIn: number }> {
    const { email } = requestOtpDto;

    // Validação de e-mail corporativo (não aceita domínios públicos)
    const publicDomains = ['gmail.com', 'hotmail.com', 'yahoo.com', 'outlook.com', 'live.com'];
    const domain = email.split('@')[1]?.toLowerCase();
    if (publicDomains.includes(domain)) {
      throw new BadRequestException('Use e-mail corporativo. Domínios públicos não são permitidos.');
    }

    // Rate limiting distribuído: máximo 3 OTPs por hora por e-mail
    const rateLimitResult = await this.redisCacheService.checkRateLimit(
      `otp:${email}`,
      3, // máximo 3 tentativas
      3600000 // 1 hora em ms
    );

    if (!rateLimitResult.allowed) {
      const resetTime = new Date(rateLimitResult.resetTime);
      throw new BadRequestException(
        `Limite de 3 OTPs por hora excedido. Tente novamente às ${resetTime.toLocaleTimeString()}.`
      );
    }

    // Gerar OTP de 6 dígitos numéricos
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    const token = uuidv4(); // Token único para identificação
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutos

    // Salvar OTP no banco
    const otp = this.otpRepository.create({
      email,
      code,
      token,
      type: OtpType.LOGIN,
      expiresAt,
      status: OtpStatus.PENDING,
    });
    await this.otpRepository.save(otp);

    // Log de auditoria
    const auditLog = this.auditLogRepository.create({
      userEmail: email,
      action: AuditAction.LOGIN,
      level: AuditLevel.INFO,
      description: `OTP requested for ${email}`,
      ipAddress: ip,
      userAgent,
      success: true,
    });
    await this.auditLogRepository.save(auditLog);

    this.logger.log(`OTP generated for ${email}: ${code}`);

    // Enviar e-mail via RabbitMQ (assíncrono e confiável)
    // const emailSent = await this.rabbitMQService.sendOtpEmail(email, code, 300);
    // if (!emailSent) {
    //   this.logger.warn(`Failed to queue OTP email for ${email}`);
    // } else {
    //   this.logger.debug(`OTP email queued successfully for ${email}`);
    // }
    this.logger.log(`OTP email would be sent to ${email} (RabbitMQ disabled for testing)`);

    return {
      message: 'Código OTP enviado para seu e-mail',
      expiresIn: 300 // 5 minutos em segundos
    };
  }

  async requestOtp(requestOtpDto: RequestOtpDto, ip: string, userAgent: string): Promise<{ message: string }> {
    const { email } = requestOtpDto;

    // Validate corporate email
    if (!this.isValidCorporateEmail(email)) {
      throw new BadRequestException('Use um e-mail corporativo válido');
    }

    // Rate limiting check
    const rateLimitKey = `otp_rate_limit:${email}`;
    const attempts = await this.cacheManager.get<number>(rateLimitKey) || 0;

    if (attempts >= 3) {
      throw new BadRequestException('Muitas tentativas. Tente novamente em 1 hora.');
    }

    // Generate OTP
    const code = this.generateOtpCode();
    const token = uuidv4();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    // Save OTP
    const otp = this.otpRepository.create({
      email,
      code,
      token,
      type: OtpType.LOGIN,
      expiresAt,
      requestIp: ip,
      userAgent,
    });

    await this.otpRepository.save(otp);

    // Update rate limit
    await this.cacheManager.set(rateLimitKey, attempts + 1, 3600); // 1 hour

    // Enviar e-mail via RabbitMQ (assíncrono e confiável)
    // const emailSent = await this.rabbitMQService.sendOtpEmail(email, code, 300);
    // if (!emailSent) {
    //   this.logger.warn(`Failed to queue OTP email for ${email}`);
    // }
    this.logger.log(`OTP email would be sent to ${email} (RabbitMQ disabled for testing)`);

    this.logger.log(`OTP generated for ${email}: ${code}`);

    // Audit log
    await this.auditLogRepository.save(
      AuditLog.createLog({
        userEmail: email,
        action: AuditAction.LOGIN,
        level: AuditLevel.INFO,
        description: `OTP requested for ${email}`,
        ipAddress: ip,
        userAgent,
        success: true,
      }),
    );

    return { message: 'Código OTP enviado para seu e-mail' };
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto, ip: string, userAgent: string): Promise<AuthResponse> {
    const { email, code } = verifyOtpDto;

    // Find valid OTP
    const otp = await this.otpRepository.findOne({
      where: {
        email,
        type: OtpType.LOGIN,
        status: OtpStatus.PENDING,
      },
      order: { createdAt: 'DESC' },
    });

    if (!otp || !otp.isValid()) {
      await this.auditLogRepository.save(
        AuditLog.loginFailed(email, ip, userAgent, 'Invalid or expired OTP'),
      );
      throw new UnauthorizedException('Código OTP inválido ou expirado');
    }

    // Validate OTP code
    const isValidCode = await otp.validateCode(code);
    if (!isValidCode) {
      await this.auditLogRepository.save(
        AuditLog.loginFailed(email, ip, userAgent, 'Invalid OTP code'),
      );
      throw new UnauthorizedException('Código OTP inválido');
    }

    // Mark OTP as used
    otp.status = OtpStatus.USED;
    otp.usedAt = new Date();
    await this.otpRepository.save(otp);

    // Find or create user
    let user = await this.userRepository.findOne({ where: { email } });
    
    if (!user) {
      // First time login - create user
      const name = email.split('@')[0]; // Use email prefix as default name
      user = this.userRepository.create({
        name,
        email,
        status: UserStatus.PENDING,
        role: UserRole.READER,
        emailVerified: true,
        emailVerifiedAt: new Date(),
      });
      await this.userRepository.save(user);

      await this.auditLogRepository.save(
        AuditLog.createLog({
          userId: user.id,
          userEmail: email,
          action: AuditAction.ACCOUNT_CREATED,
          level: AuditLevel.INFO,
          description: `Account created for ${email}`,
          ipAddress: ip,
          userAgent,
          success: true,
        }),
      );
    }

    // Update last login
    user.lastLoginAt = new Date();
    user.lastLoginIp = ip;
    await this.userRepository.save(user);

    // Generate tokens
    const tokens = await this.generateTokens(user);

    // Clear rate limit
    await this.cacheManager.del(`otp_rate_limit:${email}`);

    // Audit log
    await this.auditLogRepository.save(
      AuditLog.loginSuccess(user.id, email, ip, userAgent),
    );

    // Send welcome notification (async)
    // await this.rabbitMQService.sendPushNotification(
    //   user.id,
    //   'Login realizado com sucesso',
    //   `Bem-vindo de volta, ${user.name || user.email}!`,
    //   { loginTime: new Date().toISOString(), ip }
    // );
    this.logger.log(`Welcome notification would be sent to ${user.email} (RabbitMQ disabled for testing)`);

    return {
      user: user.toJSON(),
      ...tokens,
    };
  }

  /**
   * Autentica indústria com OTP conforme T-002 do backlog
   * - Validação de OTP com hash seguro
   * - Verificação de expiração (5 minutos)
   * - Geração de JWT com claims da indústria
   * - Filtragem automática de produtos por indústria
   * - Registro de login com auditoria
   */
  async authenticateIndustry(verifyOtpDto: VerifyOtpDto, ip: string, userAgent: string): Promise<AuthResponse> {
    const { email, code } = verifyOtpDto;

    // Find the most recent valid OTP
    const otp = await this.otpRepository.findOne({
      where: {
        email,
        status: OtpStatus.PENDING,
        type: OtpType.LOGIN,
      },
      order: { createdAt: 'DESC' },
    });

    if (!otp || !otp.isValid()) {
      await this.auditLogRepository.save(
        AuditLog.loginFailed(email, ip, userAgent, 'Invalid or expired OTP for industry authentication'),
      );
      throw new UnauthorizedException('Código OTP inválido ou expirado');
    }

    // Validate OTP code with secure hash comparison
    const isValidCode = await otp.validateCode(code);
    if (!isValidCode) {
      await this.auditLogRepository.save(
        AuditLog.loginFailed(email, ip, userAgent, 'Invalid OTP code for industry'),
      );
      throw new UnauthorizedException('Código inválido');
    }

    // Mark OTP as used
    otp.status = OtpStatus.USED;
    otp.usedAt = new Date();
    await this.otpRepository.save(otp);

    // Find or create industry user
    let user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      // First time login - create industry user
      const companyName = email.split('@')[1].split('.')[0]; // Extract company from domain
      user = this.userRepository.create({
        name: companyName,
        email,
        company: companyName,
        status: UserStatus.PENDING,
        role: UserRole.READER,
        emailVerified: true,
        emailVerifiedAt: new Date(),
        // TODO: Set industryId based on company domain mapping
      });
      await this.userRepository.save(user);

      const auditLog = this.auditLogRepository.create({
        userId: user.id,
        userEmail: email,
        action: AuditAction.ACCOUNT_CREATED,
        level: AuditLevel.INFO,
        description: `Industry user created: ${email}`,
        ipAddress: ip,
        userAgent,
        success: true,
      });
      await this.auditLogRepository.save(auditLog);
    }

    // Update last login
    user.lastLoginAt = new Date();
    user.lastLoginIp = ip;
    await this.userRepository.save(user);

    // Generate JWT with industry claims for automatic product filtering
    const tokens = await this.generateTokens(user);

    // Audit successful login
    await this.auditLogRepository.save(
      AuditLog.loginSuccess(user.id, email, ip, userAgent),
    );

    this.logger.log(`Industry authentication successful for ${email}`);

    return {
      user: user.toJSON(),
      ...tokens,
    };
  }

  /**
   * Atualiza perfil da indústria conforme T-004 do backlog
   * - Validação de dados corporativos
   * - Atualização de perfil com auditoria
   * - Versionamento de termos aceitos
   * - Validação de unicidade de e-mail
   */
  async updateIndustryProfile(userId: string, updateProfileDto: any): Promise<{ message: string; user: any }> {
    const { email, name, company, cpf, termsAccepted, termsVersion, metadata } = updateProfileDto;

    // Buscar usuário atual
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    // Validação de e-mail corporativo (não aceita domínios públicos)
    const publicDomains = ['gmail.com', 'hotmail.com', 'yahoo.com', 'outlook.com', 'live.com'];
    const domain = email.split('@')[1]?.toLowerCase();
    if (publicDomains.includes(domain)) {
      throw new BadRequestException('Use e-mail corporativo. Domínios públicos não são permitidos.');
    }

    // Validação de unicidade de e-mail (se mudou)
    if (email !== user.email) {
      const existingUser = await this.userRepository.findOne({ where: { email } });
      if (existingUser) {
        throw new ConflictException('E-mail já está em uso por outro usuário');
      }
    }

    // Validação de CPF (se fornecido)
    if (cpf) {
      const cpfNumbers = cpf.replace(/\D/g, '');
      if (cpfNumbers.length !== 11) {
        throw new BadRequestException('CPF deve ter 11 dígitos');
      }
      // TODO: Implementar validação de CPF com dígitos verificadores
    }

    // Validação de aceite de termos obrigatório
    if (!termsAccepted) {
      throw new BadRequestException('Aceite dos termos é obrigatório');
    }

    // Atualizar dados do usuário
    user.email = email;
    user.name = name;
    user.company = company;
    user.cpf = cpf;
    user.termsAcceptedAt = new Date();
    user.termsVersion = termsVersion;
    user.metadata = metadata;
    user.onboardingCompleted = true; // Marca onboarding como completo

    await this.userRepository.save(user);

    // Auditoria de atualização de perfil
    const auditLog = this.auditLogRepository.create({
      userId: user.id,
      userEmail: email,
      action: AuditAction.PROFILE_UPDATE,
      level: AuditLevel.INFO,
      description: `Industry profile updated: ${email}`,
      metadata: {
        previousEmail: user.email !== email ? user.email : undefined,
        termsVersion,
        onboardingCompleted: true
      },
      success: true,
    });
    await this.auditLogRepository.save(auditLog);

    this.logger.log(`Industry profile updated for ${email}`);

    return {
      message: 'Perfil atualizado com sucesso',
      user: user.toJSON()
    };
  }

  async register(registerDto: RegisterDto, ip: string, userAgent: string): Promise<AuthResponse> {
    const { email, password, name, cpf, company } = registerDto;

    // Check if user exists
    const existingUser = await this.userRepository.findOne({ where: { email } });
    if (existingUser) {
      throw new ConflictException('E-mail já está em uso');
    }

    // Create user
    const user = this.userRepository.create({
      email,
      password,
      name,
      cpf,
      company,
      status: UserStatus.PENDING,
      role: UserRole.READER,
    });

    await this.userRepository.save(user);

    // Generate email verification token
    const verificationToken = uuidv4();
    user.emailVerificationToken = verificationToken;
    user.emailVerificationExpiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    await this.userRepository.save(user);

    // TODO: Send verification email

    // Audit log
    await this.auditLogRepository.save(
      AuditLog.createLog({
        userId: user.id,
        userEmail: email,
        action: AuditAction.ACCOUNT_CREATED,
        level: AuditLevel.INFO,
        description: `Account registered for ${email}`,
        ipAddress: ip,
        userAgent,
        success: true,
      }),
    );

    // Generate tokens
    const tokens = await this.generateTokens(user);

    return {
      user: user.toJSON(),
      ...tokens,
    };
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<AuthResponse> {
    const { refreshToken } = refreshTokenDto;

    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
      });

      const user = await this.userRepository.findOne({
        where: { id: payload.sub, refreshToken },
      });

      if (!user) {
        throw new UnauthorizedException('Token de refresh inválido');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user);

      return {
        user: user.toJSON(),
        ...tokens,
      };
    } catch (error) {
      throw new UnauthorizedException('Token de refresh inválido');
    }
  }

  async logout(userId: string): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (user) {
      // Invalidate all user sessions in Redis
      await this.redisCacheService.invalidateAllUserSessions(userId);

      // Clear refresh token from database
      user.refreshToken = null;
      user.refreshTokenExpiresAt = null;
      await this.userRepository.save(user);

      // Audit log
      const auditLog = this.auditLogRepository.create({
        userId,
        userEmail: user.email,
        action: AuditAction.LOGOUT,
        level: AuditLevel.INFO,
        description: `User ${user.email} logged out`,
        success: true,
      });
      await this.auditLogRepository.save(auditLog);

      this.logger.log(`User ${user.email} logged out successfully`);
    }

    return { message: 'Logout realizado com sucesso' };
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<{ message: string }> {
    const { currentPassword, newPassword } = changePasswordDto;

    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    // Validate current password
    const isValidPassword = await user.validatePassword(currentPassword);
    if (!isValidPassword) {
      throw new BadRequestException('Senha atual incorreta');
    }

    // Update password
    user.password = newPassword;
    await this.userRepository.save(user);

    // Invalidate all refresh tokens
    user.refreshToken = null;
    user.refreshTokenExpiresAt = null;
    await this.userRepository.save(user);

    // Audit log
    await this.auditLogRepository.save(
      AuditLog.createLog({
        userId,
        userEmail: user.email,
        action: AuditAction.PASSWORD_CHANGE,
        level: AuditLevel.INFO,
        description: `Password changed for ${user.email}`,
        success: true,
      }),
    );

    return { message: 'Senha alterada com sucesso' };
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    const { token, newPassword } = resetPasswordDto;

    const user = await this.userRepository.findOne({
      where: {
        resetPasswordToken: token,
      },
    });

    if (!user || !user.resetPasswordExpiresAt || user.resetPasswordExpiresAt < new Date()) {
      throw new BadRequestException('Token de reset inválido ou expirado');
    }

    // Update password
    user.password = newPassword;
    user.resetPasswordToken = null;
    user.resetPasswordExpiresAt = null;
    user.refreshToken = null;
    user.refreshTokenExpiresAt = null;
    await this.userRepository.save(user);

    // Audit log
    await this.auditLogRepository.save(
      AuditLog.createLog({
        userId: user.id,
        userEmail: user.email,
        action: AuditAction.PASSWORD_RESET,
        level: AuditLevel.INFO,
        description: `Password reset for ${user.email}`,
        success: true,
      }),
    );

    return { message: 'Senha redefinida com sucesso' };
  }

  async enableTwoFactor(userId: string): Promise<{ secret: string; qrCode: string }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    const secret = speakeasy.generateSecret({
      name: `Retail Media (${user.email})`,
      issuer: 'Retail Media System',
    });

    user.twoFactorSecret = secret.base32;
    await this.userRepository.save(user);

    const qrCode = await qrcode.toDataURL(secret.otpauth_url);

    return {
      secret: secret.base32,
      qrCode,
    };
  }

  async verifyTwoFactor(userId: string, token: string): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user || !user.twoFactorSecret) {
      throw new BadRequestException('2FA não configurado');
    }

    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token,
      window: 2,
    });

    if (!verified) {
      throw new BadRequestException('Código 2FA inválido');
    }

    user.twoFactorEnabled = true;
    await this.userRepository.save(user);

    // Audit log
    await this.auditLogRepository.save(
      AuditLog.createLog({
        userId,
        userEmail: user.email,
        action: AuditAction.TWO_FACTOR_ENABLE,
        level: AuditLevel.INFO,
        description: `2FA enabled for ${user.email}`,
        success: true,
      }),
    );

    return { message: '2FA ativado com sucesso' };
  }

  /**
   * Health check para Redis
   */
  async checkRedisHealth(): Promise<{ redis: any; cache: any }> {
    const redisHealth = await this.redisCacheService.healthCheck();
    const cacheStats = await this.redisCacheService.getCacheStats();

    return {
      redis: redisHealth,
      cache: cacheStats,
    };
  }

  /**
   * Health check para RabbitMQ
   */
  async checkRabbitMQHealth(): Promise<{ rabbitmq: any; queues: any }> {
    // const rabbitmqHealth = await this.rabbitMQService.healthCheck();
    // const queueStats = await this.rabbitMQService.getQueueStats();

    return {
      rabbitmq: { status: 'disabled', message: 'RabbitMQ disabled for testing' },
      queues: { status: 'disabled', message: 'Queue stats disabled for testing' },
    };
  }

  private async generateTokens(user: User): Promise<{ accessToken: string; refreshToken: string }> {
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      industryId: user.industryId,
    };

    const accessToken = this.jwtService.sign(payload);
    const tokenId = uuidv4(); // Unique token ID for session tracking

    const refreshToken = this.jwtService.sign(
      { sub: user.id, tokenId },
      {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
        expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN', '7d'),
      },
    );

    // Save refresh token in database
    user.refreshToken = refreshToken;
    user.refreshTokenExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    await this.userRepository.save(user);

    // Cache session in Redis for fast validation and invalidation
    await this.redisCacheService.cacheSession(
      user.id,
      tokenId,
      user.refreshTokenExpiresAt
    );

    this.logger.debug(`Tokens generated for user ${user.id} with session ${tokenId}`);

    return { accessToken, refreshToken };
  }

  private generateOtpCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private isValidCorporateEmail(email: string): boolean {
    // Block common public email providers
    const publicDomains = [
      'gmail.com',
      'hotmail.com',
      'yahoo.com',
      'outlook.com',
      'live.com',
      'icloud.com',
      'protonmail.com',
    ];

    const domain = email.split('@')[1]?.toLowerCase();
    return domain && !publicDomains.includes(domain);
  }

  async getProfile(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['industry']
    });

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    return { user: user.toJSON() };
  }

  async updateProfile(userId: string, updateData: any) {
    const user = await this.userRepository.findOne({ where: { id: userId } });

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    // Store old values for audit
    const oldValues = { ...user };

    // Update allowed fields
    const allowedFields = ['name', 'cpf', 'company', 'termsAcceptedAt', 'termsVersion', 'onboardingCompleted'];
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        user[field] = updateData[field];
      }
    });

    const updatedUser = await this.userRepository.save(user);

    // Audit log
    await this.auditLogRepository.save(
      AuditLog.createLog({
        userId,
        userEmail: user.email,
        action: AuditAction.PROFILE_UPDATE,
        level: AuditLevel.INFO,
        description: `User ${user.email} updated their profile`,
        metadata: {
          oldValues: {
            name: oldValues.name,
            cpf: oldValues.cpf,
            company: oldValues.company,
          },
          newValues: {
            name: user.name,
            cpf: user.cpf,
            company: user.company,
          },
        },
        success: true,
      }),
    );

    return {
      message: 'Perfil atualizado com sucesso',
      user: updatedUser.toJSON()
    };
  }
}
