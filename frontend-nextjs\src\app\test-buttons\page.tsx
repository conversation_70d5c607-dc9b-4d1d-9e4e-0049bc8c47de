'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Plus, 
  Save, 
  Eye, 
  Settings, 
  Download,
  Upload,
  Trash2,
  Edit,
  Check,
  X
} from 'lucide-react';

export default function TestButtonsPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Teste de Botões</h1>
          <p className="text-gray-700">
            Página para testar se os botões com fundo azul têm texto e ícones brancos
          </p>
        </div>

        <div className="space-y-8">
          {/* Botões usando o componente Button do shadcn/ui */}
          <Card>
            <CardHeader>
              <CardTitle>Botões do Componente Button (shadcn/ui)</CardTitle>
              <CardDescription>
                Testando diferentes variantes do componente Button
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <Button variant="default">
                  <Plus className="h-4 w-4 mr-2" />
                  Botão Primário
                </Button>
                
                <Button variant="default" size="lg">
                  <Save className="h-4 w-4 mr-2" />
                  Botão Grande
                </Button>
                
                <Button variant="default" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  Botão Pequeno
                </Button>
                
                <Button variant="destructive">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Botão Destrutivo
                </Button>
                
                <Button variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Botão Outline
                </Button>
                
                <Button variant="secondary">
                  <Settings className="h-4 w-4 mr-2" />
                  Botão Secundário
                </Button>
                
                <Button variant="ghost">
                  <Download className="h-4 w-4 mr-2" />
                  Botão Ghost
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Botões com classes Tailwind diretas */}
          <Card>
            <CardHeader>
              <CardTitle>Botões com Classes Tailwind Diretas</CardTitle>
              <CardDescription>
                Testando botões com classes bg-blue-600, bg-blue-700, etc.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  bg-blue-600
                </button>
                
                <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-700 hover:bg-blue-800">
                  <Upload className="h-4 w-4 mr-2" />
                  bg-blue-700
                </button>
                
                <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm bg-primary text-primary-foreground hover:bg-primary/90">
                  <Check className="h-4 w-4 mr-2" />
                  bg-primary
                </button>
                
                <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                  <Settings className="h-4 w-4 mr-2" />
                  bg-indigo-600
                </button>
              </div>
            </CardContent>
          </Card>

          {/* Botões com estados disabled */}
          <Card>
            <CardHeader>
              <CardTitle>Botões Desabilitados</CardTitle>
              <CardDescription>
                Testando botões em estado disabled
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <Button variant="default" disabled>
                  <X className="h-4 w-4 mr-2" />
                  Primário Disabled
                </Button>
                
                <button 
                  disabled 
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Tailwind Disabled
                </button>
              </div>
            </CardContent>
          </Card>

          {/* Botões com ícones apenas */}
          <Card>
            <CardHeader>
              <CardTitle>Botões com Ícones Apenas</CardTitle>
              <CardDescription>
                Testando botões que contêm apenas ícones
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <Button variant="default" size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
                
                <Button variant="default" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
                
                <button className="inline-flex items-center justify-center w-9 h-9 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                  <Eye className="h-4 w-4" />
                </button>
                
                <button className="inline-flex items-center justify-center w-9 h-9 border border-transparent rounded-md shadow-sm bg-primary text-primary-foreground hover:bg-primary/90">
                  <Download className="h-4 w-4" />
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
