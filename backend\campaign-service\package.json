{"name": "campaign-service", "version": "1.0.0", "description": "Campaign Management Service for Retail Media System", "author": "Retail Media Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/config/database.config.ts", "migration:run": "typeorm-ts-node-commonjs migration:run -d src/config/database.config.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d src/config/database.config.ts", "schema:drop": "typeorm-ts-node-commonjs schema:drop -d src/config/database.config.ts", "seed:run": "ts-node src/database/seeds/run-seeds.ts"}, "dependencies": {"@nestjs/axios": "^4.0.1", "@nestjs/cache-manager": "^2.1.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^10.4.20", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.1.16", "@nestjs/throttler": "^5.0.1", "@nestjs/typeorm": "^10.0.0", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.9", "axios": "^1.6.2", "bull": "^4.12.2", "cache-manager": "^5.2.4", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cron": "^3.1.6", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "moment-timezone": "^0.5.43", "pg": "^8.11.3", "redis": "^4.6.10", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.17", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/amqplib": "^0.10.7", "@types/cron": "^2.0.1", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}