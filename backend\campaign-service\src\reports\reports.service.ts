import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan, LessThan } from 'typeorm';
import { Product } from '../database/entities/product.entity';
import { ProductCategory } from '../database/entities/product-category.entity';

export class ReportFilter {
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  industryId?: string;
  productIds?: string[];
  categoryIds?: string[];
  campaignIds?: string[];
  cycleIds?: string[];
  userTypes?: string[];
  status?: string[];
  minValue?: number;
  maxValue?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

export interface CampaignPerformanceReport {
  summary: {
    totalCampaigns: number;
    activeCampaigns: number;
    completedCampaigns: number;
    totalReach: number;
    totalEngagement: number;
    totalConversions: number;
    totalRevenue: number;
    averageROI: number;
    averageIncentive: number;
  };
  campaigns: {
    id: string;
    name: string;
    status: string;
    startDate: string;
    endDate: string;
    reach: number;
    engagement: number;
    conversions: number;
    revenue: number;
    roi: number;
    incentivePercentage: number;
    productsCount: number;
  }[];
  trends: {
    daily: { date: string; reach: number; conversions: number; revenue: number }[];
    weekly: { week: string; reach: number; conversions: number; revenue: number }[];
    monthly: { month: string; reach: number; conversions: number; revenue: number }[];
  };
  topPerformers: {
    campaigns: { id: string; name: string; roi: number }[];
    products: { id: string; name: string; conversions: number }[];
    categories: { id: string; name: string; revenue: number }[];
  };
}

export interface ProductPerformanceReport {
  summary: {
    totalProducts: number;
    activeProducts: number;
    totalSales: number;
    totalRevenue: number;
    averageRating: number;
    averagePrice: number;
  };
  products: {
    id: string;
    name: string;
    category: string;
    price: number;
    salesCount: number;
    revenue: number;
    averageRating: number;
    incentiveEligible: boolean;
    stockQuantity: number;
    lastSaleDate: string;
  }[];
  categories: {
    id: string;
    name: string;
    productsCount: number;
    totalRevenue: number;
    averageRating: number;
    topProduct: string;
  }[];
  insights: {
    bestSellers: { id: string; name: string; sales: number }[];
    topRated: { id: string; name: string; rating: number }[];
    lowStock: { id: string; name: string; stock: number }[];
    priceRanges: { range: string; count: number; revenue: number }[];
  };
}

export interface IncentiveAnalysisReport {
  summary: {
    totalIncentives: number;
    totalIncentiveValue: number;
    averageIncentivePercentage: number;
    totalSavings: number;
    conversionRate: number;
    roi: number;
  };
  incentiveRanges: {
    range: string;
    count: number;
    totalValue: number;
    averageROI: number;
    conversionRate: number;
  }[];
  productAnalysis: {
    productId: string;
    productName: string;
    incentivesApplied: number;
    totalIncentiveValue: number;
    conversions: number;
    revenue: number;
    roi: number;
  }[];
  trends: {
    monthly: { month: string; incentives: number; value: number; roi: number }[];
    seasonal: { season: string; incentives: number; value: number; roi: number }[];
  };
  recommendations: string[];
}

export interface UserActivityReport {
  summary: {
    totalUsers: number;
    activeUsers: number;
    totalSessions: number;
    averageSessionDuration: number;
    totalActions: number;
  };
  userTypes: {
    type: string;
    count: number;
    totalActions: number;
    averageSessionDuration: number;
    mostCommonActions: string[];
  }[];
  activity: {
    hourly: { hour: number; users: number; actions: number }[];
    daily: { date: string; users: number; actions: number }[];
    weekly: { week: string; users: number; actions: number }[];
  };
  topUsers: {
    userId: string;
    userType: string;
    totalActions: number;
    lastActivity: string;
    favoriteFeatures: string[];
  }[];
}

export class ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf' | 'json';
  includeCharts?: boolean;
  includeRawData?: boolean;
  compression?: boolean;
  password?: string;
}

@Injectable()
export class ReportsService {
  private readonly logger = new Logger(ReportsService.name);

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(ProductCategory)
    private readonly categoryRepository: Repository<ProductCategory>,
  ) {}

  async generateCampaignPerformanceReport(filters: ReportFilter): Promise<CampaignPerformanceReport> {
    this.logger.log('Generating campaign performance report');

    // In a real implementation, this would query actual campaign data
    // For now, we'll generate mock data based on products
    const products = await this.getFilteredProducts(filters);

    // Generate mock campaign data
    const campaigns = this.generateMockCampaigns(products, filters);
    
    const summary = this.calculateCampaignSummary(campaigns);
    const trends = this.generateCampaignTrends(filters);
    const topPerformers = this.identifyTopPerformers(campaigns, products);

    return {
      summary,
      campaigns,
      trends,
      topPerformers,
    };
  }

  async generateProductPerformanceReport(filters: ReportFilter): Promise<ProductPerformanceReport> {
    this.logger.log('Generating product performance report');

    const products = await this.getFilteredProducts(filters);
    const categories = await this.categoryRepository.find();

    const summary = this.calculateProductSummary(products);
    const categoryAnalysis = this.analyzeCategoriesByProducts(products, categories);
    const insights = this.generateProductInsights(products);

    const productData = products.map(product => ({
      id: product.id,
      name: product.name,
      category: product.category?.name || 'Sem categoria',
      price: product.price || 0,
      salesCount: product.salesCount || 0,
      revenue: (product.price || 0) * (product.salesCount || 0),
      averageRating: product.averageRating || 0,
      incentiveEligible: product.isEligibleForIncentives,
      stockQuantity: product.stockQuantity || 0,
      lastSaleDate: product.updatedAt.toISOString(),
    }));

    return {
      summary,
      products: productData,
      categories: categoryAnalysis,
      insights,
    };
  }

  async generateIncentiveAnalysisReport(filters: ReportFilter): Promise<IncentiveAnalysisReport> {
    this.logger.log('Generating incentive analysis report');

    const products = await this.getFilteredProducts(filters);
    
    // Generate mock incentive data
    const incentiveData = this.generateMockIncentiveData(products, filters);
    
    const summary = this.calculateIncentiveSummary(incentiveData);
    const incentiveRanges = this.analyzeIncentiveRanges(incentiveData);
    const productAnalysis = this.analyzeProductIncentives(incentiveData, products);
    const trends = this.generateIncentiveTrends(filters);
    const recommendations = this.generateIncentiveRecommendations(incentiveData);

    return {
      summary,
      incentiveRanges,
      productAnalysis,
      trends,
      recommendations,
    };
  }

  async generateUserActivityReport(filters: ReportFilter): Promise<UserActivityReport> {
    this.logger.log('Generating user activity report');

    // Generate mock user activity data
    const mockData = this.generateMockUserActivity(filters);

    return mockData;
  }

  async exportReport(
    reportType: 'campaign' | 'product' | 'incentive' | 'user',
    filters: ReportFilter,
    options: ExportOptions,
  ): Promise<{ downloadUrl: string; filename: string; size: number }> {
    this.logger.log(`Exporting ${reportType} report in ${options.format} format`);

    // Generate the report data
    let reportData: any;
    switch (reportType) {
      case 'campaign':
        reportData = await this.generateCampaignPerformanceReport(filters);
        break;
      case 'product':
        reportData = await this.generateProductPerformanceReport(filters);
        break;
      case 'incentive':
        reportData = await this.generateIncentiveAnalysisReport(filters);
        break;
      case 'user':
        reportData = await this.generateUserActivityReport(filters);
        break;
      default:
        throw new Error('Invalid report type');
    }

    // In a real implementation, this would:
    // 1. Convert data to the requested format
    // 2. Save file to storage (S3, local filesystem, etc.)
    // 3. Return download URL

    const filename = `${reportType}_report_${new Date().toISOString().split('T')[0]}.${options.format}`;
    const mockSize = Math.floor(Math.random() * 1000000) + 100000; // 100KB - 1MB

    return {
      downloadUrl: `/api/v1/reports/download/${filename}`,
      filename,
      size: mockSize,
    };
  }

  async getReportTemplates(): Promise<{
    id: string;
    name: string;
    description: string;
    type: string;
    filters: ReportFilter;
    schedule?: string;
  }[]> {
    // Return predefined report templates
    return [
      {
        id: 'weekly-campaign-summary',
        name: 'Resumo Semanal de Campanhas',
        description: 'Relatório semanal de performance de todas as campanhas ativas',
        type: 'campaign',
        filters: {
          dateRange: {
            startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date().toISOString(),
          },
          status: ['active', 'completed'],
        },
        schedule: 'weekly',
      },
      {
        id: 'monthly-product-analysis',
        name: 'Análise Mensal de Produtos',
        description: 'Relatório mensal detalhado de performance de produtos',
        type: 'product',
        filters: {
          dateRange: {
            startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date().toISOString(),
          },
        },
        schedule: 'monthly',
      },
      {
        id: 'incentive-roi-analysis',
        name: 'Análise de ROI de Incentivos',
        description: 'Análise detalhada do retorno sobre investimento em incentivos',
        type: 'incentive',
        filters: {
          dateRange: {
            startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date().toISOString(),
          },
        },
      },
      {
        id: 'user-engagement-report',
        name: 'Relatório de Engajamento de Usuários',
        description: 'Análise de atividade e engajamento dos usuários da plataforma',
        type: 'user',
        filters: {
          dateRange: {
            startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date().toISOString(),
          },
        },
      },
    ];
  }

  private async getFilteredProducts(filters: ReportFilter): Promise<Product[]> {
    const query = this.productRepository.createQueryBuilder('product')
      .leftJoinAndSelect('product.category', 'category');

    if (filters.industryId) {
      query.andWhere('product.industryId = :industryId', { industryId: filters.industryId });
    }

    if (filters.productIds?.length) {
      query.andWhere('product.id IN (:...productIds)', { productIds: filters.productIds });
    }

    if (filters.categoryIds?.length) {
      query.andWhere('product.categoryId IN (:...categoryIds)', { categoryIds: filters.categoryIds });
    }

    if (filters.minValue !== undefined) {
      query.andWhere('product.price >= :minValue', { minValue: filters.minValue });
    }

    if (filters.maxValue !== undefined) {
      query.andWhere('product.price <= :maxValue', { maxValue: filters.maxValue });
    }

    if (filters.sortBy) {
      query.orderBy(`product.${filters.sortBy}`, filters.sortOrder || 'DESC');
    }

    if (filters.limit) {
      query.limit(filters.limit);
    }

    if (filters.offset) {
      query.offset(filters.offset);
    }

    return query.getMany();
  }

  private generateMockCampaigns(products: Product[], filters: ReportFilter) {
    // Generate mock campaign data based on products
    const campaignCount = Math.min(10, Math.max(3, Math.floor(products.length / 5)));
    const campaigns = [];

    for (let i = 0; i < campaignCount; i++) {
      const startDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
      const endDate = new Date(startDate.getTime() + Math.random() * 14 * 24 * 60 * 60 * 1000);
      
      campaigns.push({
        id: `campaign_${i + 1}`,
        name: `Campanha ${i + 1}`,
        status: ['active', 'completed', 'paused'][Math.floor(Math.random() * 3)],
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        reach: Math.floor(Math.random() * 10000) + 1000,
        engagement: Math.floor(Math.random() * 1000) + 100,
        conversions: Math.floor(Math.random() * 100) + 10,
        revenue: Math.floor(Math.random() * 50000) + 5000,
        roi: Math.floor(Math.random() * 200) + 50,
        incentivePercentage: Math.floor(Math.random() * 25) + 5,
        productsCount: Math.floor(Math.random() * 10) + 1,
      });
    }

    return campaigns;
  }

  private calculateCampaignSummary(campaigns: any[]) {
    return {
      totalCampaigns: campaigns.length,
      activeCampaigns: campaigns.filter(c => c.status === 'active').length,
      completedCampaigns: campaigns.filter(c => c.status === 'completed').length,
      totalReach: campaigns.reduce((sum, c) => sum + c.reach, 0),
      totalEngagement: campaigns.reduce((sum, c) => sum + c.engagement, 0),
      totalConversions: campaigns.reduce((sum, c) => sum + c.conversions, 0),
      totalRevenue: campaigns.reduce((sum, c) => sum + c.revenue, 0),
      averageROI: campaigns.length > 0 ? campaigns.reduce((sum, c) => sum + c.roi, 0) / campaigns.length : 0,
      averageIncentive: campaigns.length > 0 ? campaigns.reduce((sum, c) => sum + c.incentivePercentage, 0) / campaigns.length : 0,
    };
  }

  private generateCampaignTrends(filters: ReportFilter) {
    // Generate mock trend data
    const days = 30;
    const daily = [];
    const weekly = [];
    const monthly = [];

    for (let i = 0; i < days; i++) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      daily.push({
        date: date.toISOString().split('T')[0],
        reach: Math.floor(Math.random() * 1000) + 100,
        conversions: Math.floor(Math.random() * 50) + 5,
        revenue: Math.floor(Math.random() * 5000) + 500,
      });
    }

    for (let i = 0; i < 4; i++) {
      weekly.push({
        week: `Semana ${i + 1}`,
        reach: Math.floor(Math.random() * 7000) + 1000,
        conversions: Math.floor(Math.random() * 350) + 50,
        revenue: Math.floor(Math.random() * 35000) + 5000,
      });
    }

    for (let i = 0; i < 12; i++) {
      const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
      monthly.push({
        month: monthNames[i],
        reach: Math.floor(Math.random() * 30000) + 5000,
        conversions: Math.floor(Math.random() * 1500) + 200,
        revenue: Math.floor(Math.random() * 150000) + 20000,
      });
    }

    return { daily, weekly, monthly };
  }

  private identifyTopPerformers(campaigns: any[], products: Product[]) {
    const topCampaigns = campaigns
      .sort((a, b) => b.roi - a.roi)
      .slice(0, 5)
      .map(c => ({ id: c.id, name: c.name, roi: c.roi }));

    const topProducts = products
      .sort((a, b) => (b.salesCount || 0) - (a.salesCount || 0))
      .slice(0, 5)
      .map(p => ({ id: p.id, name: p.name, conversions: p.salesCount || 0 }));

    const topCategories = [
      { id: 'cat1', name: 'Eletrônicos', revenue: 150000 },
      { id: 'cat2', name: 'Roupas', revenue: 120000 },
      { id: 'cat3', name: 'Casa e Jardim', revenue: 95000 },
    ];

    return {
      campaigns: topCampaigns,
      products: topProducts,
      categories: topCategories,
    };
  }

  private calculateProductSummary(products: Product[]) {
    return {
      totalProducts: products.length,
      activeProducts: products.filter(p => p.status === 'active').length,
      totalSales: products.reduce((sum, p) => sum + (p.salesCount || 0), 0),
      totalRevenue: products.reduce((sum, p) => sum + ((p.price || 0) * (p.salesCount || 0)), 0),
      averageRating: products.length > 0 ? products.reduce((sum, p) => sum + (p.averageRating || 0), 0) / products.length : 0,
      averagePrice: products.length > 0 ? products.reduce((sum, p) => sum + (p.price || 0), 0) / products.length : 0,
    };
  }

  private analyzeCategoriesByProducts(products: Product[], categories: ProductCategory[]) {
    // Group products by category and analyze
    const categoryMap = new Map();
    
    products.forEach(product => {
      const categoryId = product.category?.id || 'uncategorized';
      const categoryName = product.category?.name || 'Sem categoria';
      
      if (!categoryMap.has(categoryId)) {
        categoryMap.set(categoryId, {
          id: categoryId,
          name: categoryName,
          products: [],
          totalRevenue: 0,
          totalRating: 0,
        });
      }
      
      const category = categoryMap.get(categoryId);
      category.products.push(product);
      category.totalRevenue += (product.price || 0) * (product.salesCount || 0);
      category.totalRating += product.averageRating || 0;
    });

    return Array.from(categoryMap.values()).map(category => ({
      id: category.id,
      name: category.name,
      productsCount: category.products.length,
      totalRevenue: category.totalRevenue,
      averageRating: category.products.length > 0 ? category.totalRating / category.products.length : 0,
      topProduct: category.products.sort((a, b) => (b.salesCount || 0) - (a.salesCount || 0))[0]?.name || 'N/A',
    }));
  }

  private generateProductInsights(products: Product[]) {
    const bestSellers = products
      .sort((a, b) => (b.salesCount || 0) - (a.salesCount || 0))
      .slice(0, 5)
      .map(p => ({ id: p.id, name: p.name, sales: p.salesCount || 0 }));

    const topRated = products
      .sort((a, b) => (b.averageRating || 0) - (a.averageRating || 0))
      .slice(0, 5)
      .map(p => ({ id: p.id, name: p.name, rating: p.averageRating || 0 }));

    const lowStock = products
      .filter(p => (p.stockQuantity || 0) < 10)
      .sort((a, b) => (a.stockQuantity || 0) - (b.stockQuantity || 0))
      .slice(0, 5)
      .map(p => ({ id: p.id, name: p.name, stock: p.stockQuantity || 0 }));

    const priceRanges = [
      { range: '0-50', count: 0, revenue: 0 },
      { range: '51-100', count: 0, revenue: 0 },
      { range: '101-500', count: 0, revenue: 0 },
      { range: '500+', count: 0, revenue: 0 },
    ];

    products.forEach(product => {
      const price = product.price || 0;
      const revenue = price * (product.salesCount || 0);
      
      if (price <= 50) {
        priceRanges[0].count++;
        priceRanges[0].revenue += revenue;
      } else if (price <= 100) {
        priceRanges[1].count++;
        priceRanges[1].revenue += revenue;
      } else if (price <= 500) {
        priceRanges[2].count++;
        priceRanges[2].revenue += revenue;
      } else {
        priceRanges[3].count++;
        priceRanges[3].revenue += revenue;
      }
    });

    return {
      bestSellers,
      topRated,
      lowStock,
      priceRanges,
    };
  }

  private generateMockIncentiveData(products: Product[], filters: ReportFilter) {
    // Generate mock incentive data
    return products.map(product => ({
      productId: product.id,
      productName: product.name,
      incentivePercentage: Math.floor(Math.random() * 25) + 5,
      incentiveValue: (product.price || 0) * (Math.random() * 0.25 + 0.05),
      conversions: Math.floor(Math.random() * 50) + 5,
      revenue: (product.price || 0) * (Math.floor(Math.random() * 50) + 5),
      roi: Math.floor(Math.random() * 200) + 50,
    }));
  }

  private calculateIncentiveSummary(incentiveData: any[]) {
    return {
      totalIncentives: incentiveData.length,
      totalIncentiveValue: incentiveData.reduce((sum, i) => sum + i.incentiveValue, 0),
      averageIncentivePercentage: incentiveData.length > 0 ? incentiveData.reduce((sum, i) => sum + i.incentivePercentage, 0) / incentiveData.length : 0,
      totalSavings: incentiveData.reduce((sum, i) => sum + i.incentiveValue, 0),
      conversionRate: incentiveData.length > 0 ? incentiveData.reduce((sum, i) => sum + i.conversions, 0) / incentiveData.length : 0,
      roi: incentiveData.length > 0 ? incentiveData.reduce((sum, i) => sum + i.roi, 0) / incentiveData.length : 0,
    };
  }

  private analyzeIncentiveRanges(incentiveData: any[]) {
    const ranges = [
      { range: '5-10%', count: 0, totalValue: 0, totalROI: 0, totalConversions: 0 },
      { range: '11-15%', count: 0, totalValue: 0, totalROI: 0, totalConversions: 0 },
      { range: '16-20%', count: 0, totalValue: 0, totalROI: 0, totalConversions: 0 },
      { range: '21-25%', count: 0, totalValue: 0, totalROI: 0, totalConversions: 0 },
      { range: '25%+', count: 0, totalValue: 0, totalROI: 0, totalConversions: 0 },
    ];

    incentiveData.forEach(item => {
      const percentage = item.incentivePercentage;
      let rangeIndex = 0;
      
      if (percentage <= 10) rangeIndex = 0;
      else if (percentage <= 15) rangeIndex = 1;
      else if (percentage <= 20) rangeIndex = 2;
      else if (percentage <= 25) rangeIndex = 3;
      else rangeIndex = 4;

      ranges[rangeIndex].count++;
      ranges[rangeIndex].totalValue += item.incentiveValue;
      ranges[rangeIndex].totalROI += item.roi;
      ranges[rangeIndex].totalConversions += item.conversions;
    });

    return ranges.map(range => ({
      range: range.range,
      count: range.count,
      totalValue: range.totalValue,
      averageROI: range.count > 0 ? range.totalROI / range.count : 0,
      conversionRate: range.count > 0 ? range.totalConversions / range.count : 0,
    }));
  }

  private analyzeProductIncentives(incentiveData: any[], products: Product[]) {
    return incentiveData.map(item => ({
      productId: item.productId,
      productName: item.productName,
      incentivesApplied: 1, // Simplified
      totalIncentiveValue: item.incentiveValue,
      conversions: item.conversions,
      revenue: item.revenue,
      roi: item.roi,
    }));
  }

  private generateIncentiveTrends(filters: ReportFilter) {
    const monthly = [];
    const seasonal = [];

    for (let i = 0; i < 12; i++) {
      const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
      monthly.push({
        month: monthNames[i],
        incentives: Math.floor(Math.random() * 100) + 20,
        value: Math.floor(Math.random() * 10000) + 2000,
        roi: Math.floor(Math.random() * 150) + 50,
      });
    }

    const seasons = ['Primavera', 'Verão', 'Outono', 'Inverno'];
    seasons.forEach(season => {
      seasonal.push({
        season,
        incentives: Math.floor(Math.random() * 300) + 100,
        value: Math.floor(Math.random() * 30000) + 10000,
        roi: Math.floor(Math.random() * 200) + 75,
      });
    });

    return { monthly, seasonal };
  }

  private generateIncentiveRecommendations(incentiveData: any[]): string[] {
    const recommendations = [];
    
    const avgROI = incentiveData.reduce((sum, i) => sum + i.roi, 0) / incentiveData.length;
    const avgIncentive = incentiveData.reduce((sum, i) => sum + i.incentivePercentage, 0) / incentiveData.length;

    if (avgROI > 150) {
      recommendations.push('ROI excelente! Considere expandir campanhas similares.');
    } else if (avgROI < 75) {
      recommendations.push('ROI baixo. Revise estratégia de incentivos.');
    }

    if (avgIncentive > 20) {
      recommendations.push('Incentivos altos podem impactar margem. Considere otimização.');
    } else if (avgIncentive < 10) {
      recommendations.push('Incentivos baixos podem limitar atratividade. Considere aumento.');
    }

    recommendations.push('Monitore sazonalidade para otimizar timing das campanhas.');
    recommendations.push('Teste A/B diferentes percentuais de incentivo para otimizar ROI.');

    return recommendations;
  }

  private generateMockUserActivity(filters: ReportFilter): UserActivityReport {
    // Generate mock user activity data
    return {
      summary: {
        totalUsers: 1250,
        activeUsers: 890,
        totalSessions: 15420,
        averageSessionDuration: 1847, // seconds
        totalActions: 45680,
      },
      userTypes: [
        {
          type: 'admin',
          count: 15,
          totalActions: 8500,
          averageSessionDuration: 2400,
          mostCommonActions: ['create_campaign', 'view_reports', 'manage_users'],
        },
        {
          type: 'editor',
          count: 125,
          totalActions: 25000,
          averageSessionDuration: 1950,
          mostCommonActions: ['edit_campaign', 'view_analytics', 'manage_products'],
        },
        {
          type: 'reader',
          count: 750,
          totalActions: 12180,
          averageSessionDuration: 1200,
          mostCommonActions: ['view_dashboard', 'view_reports', 'search_products'],
        },
      ],
      activity: {
        hourly: Array.from({ length: 24 }, (_, i) => ({
          hour: i,
          users: Math.floor(Math.random() * 100) + 20,
          actions: Math.floor(Math.random() * 500) + 100,
        })),
        daily: Array.from({ length: 7 }, (_, i) => ({
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          users: Math.floor(Math.random() * 200) + 50,
          actions: Math.floor(Math.random() * 1000) + 200,
        })),
        weekly: Array.from({ length: 4 }, (_, i) => ({
          week: `Semana ${i + 1}`,
          users: Math.floor(Math.random() * 800) + 200,
          actions: Math.floor(Math.random() * 5000) + 1000,
        })),
      },
      topUsers: [
        {
          userId: 'user1',
          userType: 'admin',
          totalActions: 1250,
          lastActivity: new Date().toISOString(),
          favoriteFeatures: ['reports', 'campaign_management', 'user_management'],
        },
        {
          userId: 'user2',
          userType: 'editor',
          totalActions: 980,
          lastActivity: new Date(Date.now() - 3600000).toISOString(),
          favoriteFeatures: ['product_selection', 'incentive_calculator', 'analytics'],
        },
      ],
    };
  }
}
