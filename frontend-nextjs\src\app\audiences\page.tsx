'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowLeft, Plus, Users, Target, TrendingUp, Edit, Trash2 } from 'lucide-react';

export default function AudiencesPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  const handleBack = () => {
    router.push('/dashboard');
  };

  const audiences = [
    {
      id: 1,
      name: '<PERSON>lheres Jovens Urbanas',
      description: 'Mulheres de 25-35 anos em grandes centros urbanos',
      size: '1.2M',
      engagement: '8.5%',
      conversion: '3.2%',
      status: 'Ativa',
      campaigns: 5
    },
    {
      id: 2,
      name: 'Homens Executivos',
      description: 'Homens de 30-50 anos com alta renda',
      size: '850K',
      engagement: '6.8%',
      conversion: '4.1%',
      status: 'Ativa',
      campaigns: 3
    },
    {
      id: 3,
      name: 'Famílias com Crianças',
      description: 'Pais e mães com filhos de 0-12 anos',
      size: '2.1M',
      engagement: '12.3%',
      conversion: '2.8%',
      status: 'Ativa',
      campaigns: 8
    },
    {
      id: 4,
      name: 'Jovens Universitários',
      description: 'Estudantes universitários de 18-25 anos',
      size: '650K',
      engagement: '15.2%',
      conversion: '1.9%',
      status: 'Pausada',
      campaigns: 2
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={handleBack}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <h1 className="text-xl font-bold text-gray-900">
                <span className="text-red-600">Retail Media</span> Platform
              </h1>
            </div>
            
            <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Nova Audiência
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Audiências</h2>
          <p className="text-gray-700">
            Gerencie e analise suas audiências para campanhas mais efetivas.
          </p>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-700">Total de Audiências</p>
                <p className="text-2xl font-bold text-gray-900">4</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Target className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-700">Alcance Total</p>
                <p className="text-2xl font-bold text-gray-900">4.8M</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-700">Engajamento Médio</p>
                <p className="text-2xl font-bold text-gray-900">10.7%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Target className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-700">Conversão Média</p>
                <p className="text-2xl font-bold text-gray-900">3.0%</p>
              </div>
            </div>
          </div>
        </div>

        {/* Audiences List */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Suas Audiências</h3>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Audiência
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tamanho
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Engajamento
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Conversão
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Campanhas
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {audiences.map((audience) => (
                  <tr key={audience.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{audience.name}</div>
                        <div className="text-sm text-gray-700">{audience.description}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {audience.size}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {audience.engagement}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {audience.conversion}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {audience.campaigns}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        audience.status === 'Ativa' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {audience.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Insights */}
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Insights de Performance</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-blue-900">Melhor Engajamento</p>
                  <p className="text-xs text-blue-700">Jovens Universitários - 15.2%</p>
                </div>
                <TrendingUp className="h-5 w-5 text-blue-600" />
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-green-900">Melhor Conversão</p>
                  <p className="text-xs text-green-700">Homens Executivos - 4.1%</p>
                </div>
                <Target className="h-5 w-5 text-green-600" />
              </div>
              
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-purple-900">Maior Alcance</p>
                  <p className="text-xs text-purple-700">Famílias com Crianças - 2.1M</p>
                </div>
                <Users className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recomendações</h3>
            <div className="space-y-3">
              <div className="p-3 border-l-4 border-blue-500 bg-blue-50">
                <p className="text-sm text-blue-800">
                  <strong>Otimize para Jovens Universitários:</strong> Alto engajamento mas baixa conversão. 
                  Considere ajustar a estratégia de preços.
                </p>
              </div>
              
              <div className="p-3 border-l-4 border-green-500 bg-green-50">
                <p className="text-sm text-green-800">
                  <strong>Expanda Homens Executivos:</strong> Excelente conversão. 
                  Considere aumentar o investimento nesta audiência.
                </p>
              </div>
              
              <div className="p-3 border-l-4 border-yellow-500 bg-yellow-50">
                <p className="text-sm text-yellow-800">
                  <strong>Reative Audiência Pausada:</strong> Jovens Universitários tem potencial. 
                  Considere reativar com nova estratégia.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
