import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { IsString, IsArray, IsNumber, <PERSON>, <PERSON> } from 'class-validator';
import { 
  AIEstimatorService, 
  CampaignEstimationInput 
} from './ai-estimator.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';

export class QuickEstimationDto {
  @IsString()
  industryId: string;

  @IsArray()
  @IsString({ each: true })
  productIds: string[];

  @IsNumber()
  @Min(0.1)
  @Max(100)
  incentivePercentage: number;
}

export class BulkEstimationDto {
  industryId: string;
  scenarios: {
    name: string;
    productIds: string[];
    incentivePercentage: number;
    validityDays: number;
    budget?: number;
  }[];
}

@ApiTags('AI Estimator')
@Controller('ai-estimator')
// @UseGuards(JwtAuthGuard) // Commented out for now
// @ApiBearerAuth()
export class AIEstimatorController {
  constructor(private readonly aiEstimatorService: AIEstimatorService) {}

  @Post('test')
  @ApiOperation({ summary: 'Teste simples do serviço de IA' })
  async testService() {
    return {
      status: 'ok',
      message: 'AI Estimator Service is working',
      timestamp: new Date().toISOString()
    };
  }

  @Post('estimate-performance')
  @ApiOperation({ summary: 'Estimar performance de campanha usando IA' })
  @ApiResponse({
    status: 200,
    description: 'Estimativa de performance gerada',
    schema: {
      type: 'object',
      properties: {
        estimatedReach: { type: 'number' },
        estimatedEngagement: { type: 'number' },
        estimatedConversions: { type: 'number' },
        estimatedRevenue: { type: 'number' },
        estimatedROI: { type: 'number' },
        confidenceScore: { type: 'number' },
        factors: {
          type: 'object',
          properties: {
            productPopularity: { type: 'number' },
            incentiveAttractiveness: { type: 'number' },
            seasonality: { type: 'number' },
            marketTrends: { type: 'number' },
            historicalPerformance: { type: 'number' }
          }
        },
        recommendations: { type: 'array', items: { type: 'string' } },
        warnings: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 429, description: 'Limite de estimativas excedido, tente em 1 hora' })
  @ApiResponse({ status:503, description: 'Serviço temporariamente indisponível' })
  async estimatePerformance(@Body() input: CampaignEstimationInput) {
    try {
      // Implementação simplificada para demonstração
      const baseReach = input.productIds.length * 1000;
      const incentiveMultiplier = input.incentivePercentage / 10;
      const validityMultiplier = Math.min(input.validityDays / 30, 2);

      const estimatedReach = Math.round(baseReach * incentiveMultiplier * validityMultiplier);
      const estimatedConversions = Math.round(estimatedReach * 0.05); // 5% conversion rate
      const estimatedRevenue = estimatedConversions * 50; // R$ 50 per conversion
      const estimatedROI = ((estimatedRevenue - (estimatedConversions * input.incentivePercentage * 0.5)) / (estimatedConversions * input.incentivePercentage * 0.5)) * 100;

      return {
        estimatedReach,
        estimatedEngagement: Math.round(estimatedReach * 0.15),
        estimatedConversions,
        estimatedRevenue,
        estimatedROI: Math.round(estimatedROI * 10) / 10,
        confidenceScore: 85,
        factors: {
          productPopularity: 75,
          incentiveAttractiveness: input.incentivePercentage > 10 ? 85 : 65,
          seasonality: 80,
          marketTrends: 70,
          historicalPerformance: 75
        },
        recommendations: [
          input.incentivePercentage < 10 ? 'Considere aumentar o incentivo para melhor performance' : 'Incentivo adequado para boa performance',
          input.validityDays < 15 ? 'Considere estender a validade da campanha' : 'Duração adequada da campanha'
        ],
        warnings: estimatedROI < 50 ? ['ROI baixo detectado - revise a estratégia'] : []
      };
    } catch (error) {
      console.error('Error in estimatePerformance:', error);

      // Handle specific S-008 error cases
      if (error.message.includes('Padrão anômalo')) {
        throw new Error('Padrão anômalo detectado - custo 10x maior que histórico');
      }

      if (error.message.includes('Limite de estimativas')) {
        throw new Error('Limite de estimativas excedido, tente em 1 hora');
      }

      if (error.message.includes('Dados insuficientes')) {
        throw new Error('Dados insuficientes para estimativa confiável');
      }

      throw new Error(`Serviço temporariamente indisponível: ${error.message}`);
    }
  }

  @Post('quick-estimate')
  @ApiOperation({ summary: 'Estimativa rápida de performance' })
  @ApiResponse({ 
    status: 200, 
    description: 'Estimativa rápida gerada',
    schema: {
      type: 'object',
      properties: {
        estimatedROI: { type: 'number' },
        estimatedRevenue: { type: 'number' },
        confidenceScore: { type: 'number' },
        recommendation: { type: 'string' }
      }
    }
  })
  async quickEstimate(@Body() input: QuickEstimationDto) {
    try {
      // Implementação simplificada para demonstração
      const baseRevenue = input.productIds.length * 2500; // R$ 2500 per product
      const incentiveMultiplier = input.incentivePercentage / 10;

      const estimatedRevenue = Math.round(baseRevenue * incentiveMultiplier);
      const estimatedCost = Math.round(estimatedRevenue * (input.incentivePercentage / 100) * 0.3);
      const estimatedROI = Math.round(((estimatedRevenue - estimatedCost) / estimatedCost) * 100 * 10) / 10;

      let recommendation = 'Campanha viável';
      if (estimatedROI > 200) {
        recommendation = 'Excelente potencial de retorno';
      } else if (estimatedROI < 50) {
        recommendation = 'Considere ajustar incentivo ou produtos';
      }

      return {
        estimatedROI,
        estimatedRevenue,
        confidenceScore: 80,
        recommendation,
      };
    } catch (error) {
      throw new Error('Serviço temporariamente indisponível');
    }
  }

  @Post('compare-scenarios')
  @ApiOperation({ summary: 'Comparar múltiplos cenários de campanha' })
  @ApiResponse({ 
    status: 200, 
    description: 'Comparação de cenários',
    schema: {
      type: 'object',
      properties: {
        scenarios: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              estimatedROI: { type: 'number' },
              estimatedRevenue: { type: 'number' },
              estimatedReach: { type: 'number' },
              confidenceScore: { type: 'number' },
              ranking: { type: 'number' }
            }
          }
        },
        bestScenario: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            reason: { type: 'string' }
          }
        },
        insights: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async compareScenarios(@Body() input: BulkEstimationDto) {
    const results = [];

    for (const scenario of input.scenarios) {
      const estimation = await this.aiEstimatorService.estimateCampaignPerformance({
        industryId: input.industryId,
        productIds: scenario.productIds,
        incentivePercentage: scenario.incentivePercentage,
        validityDays: scenario.validityDays,
        budget: scenario.budget,
      });

      results.push({
        name: scenario.name,
        estimatedROI: estimation.estimatedROI,
        estimatedRevenue: estimation.estimatedRevenue,
        estimatedReach: estimation.estimatedReach,
        confidenceScore: estimation.confidenceScore,
        fullEstimation: estimation,
      });
    }

    // Rank scenarios by ROI and confidence
    results.sort((a, b) => {
      const scoreA = a.estimatedROI * (a.confidenceScore / 100);
      const scoreB = b.estimatedROI * (b.confidenceScore / 100);
      return scoreB - scoreA;
    });

    results.forEach((result, index) => {
      result.ranking = index + 1;
    });

    const bestScenario = results[0];
    const insights = [
      `Melhor cenário: ${bestScenario.name} com ROI de ${bestScenario.estimatedROI.toFixed(1)}%`,
      `Diferença de performance entre melhor e pior: ${(results[0].estimatedROI - results[results.length - 1].estimatedROI).toFixed(1)}%`,
      `Confiança média das estimativas: ${(results.reduce((sum, r) => sum + r.confidenceScore, 0) / results.length).toFixed(1)}%`,
    ];

    return {
      scenarios: results.map(r => ({
        name: r.name,
        estimatedROI: r.estimatedROI,
        estimatedRevenue: r.estimatedRevenue,
        estimatedReach: r.estimatedReach,
        confidenceScore: r.confidenceScore,
        ranking: r.ranking,
      })),
      bestScenario: {
        name: bestScenario.name,
        reason: `Maior ROI ajustado por confiança (${(bestScenario.estimatedROI * bestScenario.confidenceScore / 100).toFixed(1)})`,
      },
      insights,
    };
  }

  @Get('suggestions/:industryId')
  @ApiOperation({ summary: 'Obter sugestões de campanhas geradas por IA' })
  @ApiQuery({ name: 'limit', required: false, type: 'number', description: 'Número máximo de sugestões' })
  @ApiResponse({ 
    status: 200, 
    description: 'Sugestões de campanhas',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          description: { type: 'string' },
          suggestedProducts: { type: 'array', items: { type: 'string' } },
          suggestedIncentive: { type: 'number' },
          suggestedDuration: { type: 'number' },
          estimatedPerformance: { type: 'object' },
          reasoning: { type: 'array', items: { type: 'string' } },
          priority: { type: 'string', enum: ['high', 'medium', 'low'] },
          category: { type: 'string' }
        }
      }
    }
  })
  async getCampaignSuggestions(
    @Param('industryId') industryId: string,
    @Query('limit') limit?: number,
  ) {
    return this.aiEstimatorService.generateCampaignSuggestions(
      industryId, 
      limit ? parseInt(limit.toString()) : 5
    );
  }

  @Get('insights/:industryId')
  @ApiOperation({ summary: 'Obter insights de mercado gerados por IA' })
  @ApiResponse({ 
    status: 200, 
    description: 'Insights de mercado',
    schema: {
      type: 'object',
      properties: {
        marketTrends: {
          type: 'object',
          properties: {
            trending: { type: 'array', items: { type: 'string' } },
            declining: { type: 'array', items: { type: 'string' } },
            seasonal: { type: 'array', items: { type: 'string' } }
          }
        },
        competitorAnalysis: {
          type: 'object',
          properties: {
            averageIncentive: { type: 'number' },
            popularProducts: { type: 'array', items: { type: 'string' } },
            successfulStrategies: { type: 'array', items: { type: 'string' } }
          }
        },
        optimization: {
          type: 'object',
          properties: {
            bestTimeToLaunch: { type: 'string' },
            optimalIncentiveRange: { type: 'array', items: { type: 'number' } },
            recommendedDuration: { type: 'number' },
            targetAudienceInsights: { type: 'array', items: { type: 'string' } }
          }
        },
        riskFactors: {
          type: 'object',
          properties: {
            level: { type: 'string', enum: ['low', 'medium', 'high'] },
            factors: { type: 'array', items: { type: 'string' } },
            mitigation: { type: 'array', items: { type: 'string' } }
          }
        }
      }
    }
  })
  async getAIInsights(@Param('industryId') industryId: string) {
    return this.aiEstimatorService.getAIInsights(industryId);
  }

  @Get('market-analysis/:industryId')
  @ApiOperation({ summary: 'Análise completa de mercado com IA' })
  @ApiResponse({ 
    status: 200, 
    description: 'Análise completa de mercado',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            marketHealth: { type: 'string' },
            opportunityScore: { type: 'number' },
            competitionLevel: { type: 'string' },
            recommendedAction: { type: 'string' }
          }
        },
        insights: { type: 'object' },
        suggestions: { type: 'array', items: { type: 'object' } },
        trends: { type: 'object' }
      }
    }
  })
  async getMarketAnalysis(@Param('industryId') industryId: string) {
    const [insights, suggestions] = await Promise.all([
      this.aiEstimatorService.getAIInsights(industryId),
      this.aiEstimatorService.generateCampaignSuggestions(industryId, 3),
    ]);

    // Calculate market health score
    const opportunityScore = suggestions.reduce((sum, s) => sum + s.estimatedPerformance.estimatedROI, 0) / suggestions.length;
    
    let marketHealth = 'Bom';
    if (opportunityScore > 150) marketHealth = 'Excelente';
    else if (opportunityScore > 100) marketHealth = 'Muito Bom';
    else if (opportunityScore < 50) marketHealth = 'Desafiador';

    let competitionLevel = 'Média';
    if (insights.competitorAnalysis.averageIncentive > 20) competitionLevel = 'Alta';
    else if (insights.competitorAnalysis.averageIncentive < 10) competitionLevel = 'Baixa';

    let recommendedAction = 'Prosseguir com campanhas planejadas';
    if (opportunityScore > 150) recommendedAction = 'Acelerar investimentos em campanhas';
    else if (opportunityScore < 50) recommendedAction = 'Revisar estratégia e aguardar melhor momento';

    return {
      summary: {
        marketHealth,
        opportunityScore: Math.round(opportunityScore),
        competitionLevel,
        recommendedAction,
      },
      insights,
      suggestions: suggestions.slice(0, 3),
      trends: insights.marketTrends,
    };
  }

  @Post('optimize-campaign')
  @ApiOperation({ summary: 'Otimizar campanha existente com IA' })
  @ApiResponse({ 
    status: 200, 
    description: 'Sugestões de otimização',
    schema: {
      type: 'object',
      properties: {
        currentPerformance: { type: 'object' },
        optimizedScenario: { type: 'object' },
        improvements: {
          type: 'object',
          properties: {
            roiImprovement: { type: 'number' },
            reachImprovement: { type: 'number' },
            conversionImprovement: { type: 'number' }
          }
        },
        recommendations: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async optimizeCampaign(@Body() currentCampaign: CampaignEstimationInput) {
    // Get current performance
    const currentPerformance = await this.aiEstimatorService.estimateCampaignPerformance(currentCampaign);

    // Generate optimized scenario
    const optimizedIncentive = Math.min(30, currentCampaign.incentivePercentage * 1.2);
    const optimizedCampaign: CampaignEstimationInput = {
      ...currentCampaign,
      incentivePercentage: optimizedIncentive,
      validityDays: Math.min(45, currentCampaign.validityDays * 1.5),
    };

    const optimizedPerformance = await this.aiEstimatorService.estimateCampaignPerformance(optimizedCampaign);

    // Calculate improvements
    const roiImprovement = ((optimizedPerformance.estimatedROI - currentPerformance.estimatedROI) / currentPerformance.estimatedROI) * 100;
    const reachImprovement = ((optimizedPerformance.estimatedReach - currentPerformance.estimatedReach) / currentPerformance.estimatedReach) * 100;
    const conversionImprovement = ((optimizedPerformance.estimatedConversions - currentPerformance.estimatedConversions) / currentPerformance.estimatedConversions) * 100;

    const recommendations = [
      `Aumentar incentivo para ${optimizedIncentive.toFixed(1)}% pode melhorar ROI em ${roiImprovement.toFixed(1)}%`,
      `Estender duração para ${optimizedCampaign.validityDays} dias pode aumentar alcance em ${reachImprovement.toFixed(1)}%`,
      ...optimizedPerformance.recommendations,
    ];

    return {
      currentPerformance,
      optimizedScenario: optimizedPerformance,
      improvements: {
        roiImprovement: Math.round(roiImprovement * 10) / 10,
        reachImprovement: Math.round(reachImprovement * 10) / 10,
        conversionImprovement: Math.round(conversionImprovement * 10) / 10,
      },
      recommendations,
    };
  }
}
