'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowLeft, Download, Filter, Calendar, BarChart3, TrendingUp, Eye, MousePointer } from 'lucide-react';

export default function ReportsPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  const handleBack = () => {
    router.push('/dashboard');
  };

  const reports = [
    {
      title: 'Performance de Campanhas',
      description: 'Anális<PERSON> detalhada do desempenho de todas as campanhas ativas',
      metrics: ['CTR: 2.1%', 'CPC: R$ 0.85', 'ROAS: 4.2x'],
      icon: BarChart3,
      color: 'text-blue-600'
    },
    {
      title: 'Análise de Audiência',
      description: 'Insights sobre comportamento e engajamento da audiência',
      metrics: ['Alcance: 2.4M', 'Engajamento: 15%', 'Conversão: 2.5%'],
      icon: Eye,
      color: 'text-green-600'
    },
    {
      title: 'ROI por Categoria',
      description: 'Retorno sobre investimento segmentado por categoria de produto',
      metrics: ['Eletrônicos: 5.2x', 'Moda: 3.8x', 'Casa: 4.1x'],
      icon: TrendingUp,
      color: 'text-purple-600'
    },
    {
      title: 'Funil de Conversão',
      description: 'Análise do funil desde impressão até conversão',
      metrics: ['Impressões: 2.4M', 'Cliques: 48K', 'Conversões: 1.2K'],
      icon: MousePointer,
      color: 'text-orange-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={handleBack}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <h1 className="text-xl font-bold text-gray-900">
                <span className="text-red-600">Retail Media</span> Platform
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <Filter className="h-4 w-4 mr-2" />
                Filtros
              </button>
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <Calendar className="h-4 w-4 mr-2" />
                Período
              </button>
              <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Relatórios</h2>
          <p className="text-gray-700">
            Análise detalhada do desempenho das suas campanhas de retail media.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-700">Receita Total</p>
                <p className="text-2xl font-bold text-gray-900">R$ 125.4K</p>
                <p className="text-xs text-green-700">+18% vs mês anterior</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Eye className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-700">Impressões</p>
                <p className="text-2xl font-bold text-gray-900">2.4M</p>
                <p className="text-xs text-green-700">+15% vs mês anterior</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <MousePointer className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-700">CTR Médio</p>
                <p className="text-2xl font-bold text-gray-900">2.1%</p>
                <p className="text-xs text-green-700">+0.3% vs mês anterior</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-700">ROAS</p>
                <p className="text-2xl font-bold text-gray-900">4.2x</p>
                <p className="text-xs text-green-700">+0.5x vs mês anterior</p>
              </div>
            </div>
          </div>
        </div>

        {/* Reports Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {reports.map((report, index) => {
            const Icon = report.icon;
            return (
              <div key={index} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <Icon className={`h-6 w-6 ${report.color}`} />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-gray-900">{report.title}</h3>
                      <p className="text-sm text-gray-700">{report.description}</p>
                    </div>
                  </div>
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    Ver detalhes
                  </button>
                </div>
                
                <div className="space-y-2">
                  {report.metrics.map((metric, metricIndex) => (
                    <div key={metricIndex} className="flex justify-between items-center py-1">
                      <span className="text-sm text-gray-800">{metric.split(':')[0]}:</span>
                      <span className="text-sm font-medium text-gray-900">{metric.split(':')[1]}</span>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <button className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <Download className="h-4 w-4 mr-2" />
                    Baixar Relatório
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        {/* Chart Placeholder */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance ao Longo do Tempo</h3>
          <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-gray-500 mx-auto mb-2" />
              <p className="text-gray-700">Gráfico de performance será exibido aqui</p>
              <p className="text-sm text-gray-600">Integração com biblioteca de gráficos em desenvolvimento</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
