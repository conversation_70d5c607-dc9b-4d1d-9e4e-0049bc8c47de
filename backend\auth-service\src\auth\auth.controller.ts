import {
  Controller,
  Post,
  Put,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Request,
  Get,
  Patch,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';

import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { RequestOtpDto } from './dto/request-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { UpdateIndustryProfileDto } from './dto/update-industry-profile.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AuthResponse } from './interfaces/auth-response.interface';

@ApiTags('auth')
@Controller('auth')
@UseGuards(ThrottlerGuard)
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('industries/request-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Solicitar código OTP para indústria',
    description: 'Endpoint implementado conforme T-001 do backlog. Envia OTP de 6 dígitos para e-mail corporativo com validação e rate limiting.'
  })
  @ApiResponse({
    status: 200,
    description: 'OTP enviado com sucesso para e-mail corporativo',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Código OTP enviado para seu e-mail' },
        expiresIn: { type: 'number', example: 300, description: 'Expiração em segundos (5 minutos)' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'E-mail inválido ou domínio público não permitido' })
  @ApiResponse({ status: 429, description: 'Rate limit excedido - máximo 3 OTPs por hora por e-mail' })
  async requestOtpForIndustry(@Body() requestOtpDto: RequestOtpDto, @Request() req): Promise<{ message: string; expiresIn: number }> {
    const ip = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || '';
    return this.authService.requestOtpForIndustry(requestOtpDto, ip, userAgent);
  }

  @Post('industries/authenticate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Autenticar indústria com código OTP',
    description: 'Endpoint implementado conforme T-002 do backlog. Valida OTP e gera JWT com claims da indústria para filtragem automática de produtos.'
  })
  @ApiResponse({
    status: 200,
    description: 'Autenticação realizada com sucesso',
    schema: {
      type: 'object',
      properties: {
        user: {
          type: 'object',
          description: 'Dados do usuário da indústria'
        },
        accessToken: {
          type: 'string',
          description: 'JWT com claims da indústria para filtragem de produtos'
        },
        refreshToken: {
          type: 'string',
          description: 'Token para renovação'
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Código OTP inválido ou expirado' })
  @ApiResponse({ status: 400, description: 'Dados de entrada inválidos' })
  async authenticateIndustry(@Body() verifyOtpDto: VerifyOtpDto, @Request() req): Promise<AuthResponse> {
    const ip = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || '';
    return this.authService.authenticateIndustry(verifyOtpDto, ip, userAgent);
  }

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Registrar novo usuário' })
  @ApiResponse({ status: 201, description: 'Usuário criado com sucesso' })
  @ApiResponse({ status: 409, description: 'E-mail já está em uso' })
  async register(@Body() registerDto: RegisterDto, @Request() req): Promise<AuthResponse> {
    const ip = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || '';
    return this.authService.register(registerDto, ip, userAgent);
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Login com e-mail e senha' })
  @ApiResponse({ status: 200, description: 'Login realizado com sucesso' })
  @ApiResponse({ status: 401, description: 'Credenciais inválidas' })
  async login(@Body() loginDto: LoginDto, @Request() req): Promise<AuthResponse> {
    // TODO: Implement traditional login
    throw new Error('Use OTP login instead');
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Renovar token de acesso' })
  @ApiResponse({ status: 200, description: 'Token renovado com sucesso' })
  @ApiResponse({ status: 401, description: 'Token de refresh inválido' })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto): Promise<AuthResponse> {
    return this.authService.refreshToken(refreshTokenDto);
  }

  @Put('industries/profile')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Atualizar perfil da indústria',
    description: 'Endpoint implementado conforme T-004 do backlog. Validação de dados corporativos, aceite de termos e auditoria.'
  })
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: 'Perfil atualizado com sucesso',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Perfil atualizado com sucesso' },
        user: { type: 'object', description: 'Dados atualizados do usuário' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos, e-mail corporativo obrigatório ou CPF inválido' })
  @ApiResponse({ status: 409, description: 'E-mail já está em uso por outro usuário' })
  async updateIndustryProfile(@Body() updateProfileDto: UpdateIndustryProfileDto, @Request() req): Promise<{ message: string; user: any }> {
    return this.authService.updateIndustryProfile(req.user.sub, updateProfileDto);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Fazer logout' })
  @ApiResponse({ status: 200, description: 'Logout realizado com sucesso' })
  async logout(@Request() req): Promise<{ message: string }> {
    return this.authService.logout(req.user.sub);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obter perfil do usuário logado' })
  @ApiResponse({ status: 200, description: 'Perfil do usuário' })
  async getProfile(@Request() req) {
    return this.authService.getProfile(req.user.sub);
  }

  @Get('health/redis')
  @ApiOperation({ summary: 'Verificar saúde do Redis' })
  @ApiResponse({ status: 200, description: 'Status do Redis' })
  async checkRedisHealth() {
    return this.authService.checkRedisHealth();
  }

  @Get('health/rabbitmq')
  @ApiOperation({ summary: 'Verificar saúde do RabbitMQ' })
  @ApiResponse({ status: 200, description: 'Status do RabbitMQ' })
  async checkRabbitMQHealth() {
    return this.authService.checkRabbitMQHealth();
  }

  @Patch('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Atualizar perfil do usuário logado' })
  @ApiResponse({ status: 200, description: 'Perfil atualizado com sucesso' })
  async updateProfile(@Body() updateData: any, @Request() req) {
    return this.authService.updateProfile(req.user.sub, updateData);
  }

  @Patch('change-password')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Alterar senha' })
  @ApiResponse({ status: 200, description: 'Senha alterada com sucesso' })
  @ApiResponse({ status: 400, description: 'Senha atual incorreta' })
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @Request() req,
  ): Promise<{ message: string }> {
    return this.authService.changePassword(req.user.sub, changePasswordDto);
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Redefinir senha com token' })
  @ApiResponse({ status: 200, description: 'Senha redefinida com sucesso' })
  @ApiResponse({ status: 400, description: 'Token inválido ou expirado' })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @Post('2fa/enable')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Habilitar autenticação de dois fatores' })
  @ApiResponse({ status: 200, description: 'QR Code para configurar 2FA' })
  async enableTwoFactor(@Request() req): Promise<{ secret: string; qrCode: string }> {
    return this.authService.enableTwoFactor(req.user.sub);
  }

  @Post('2fa/verify')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Verificar e ativar 2FA' })
  @ApiResponse({ status: 200, description: '2FA ativado com sucesso' })
  @ApiResponse({ status: 400, description: 'Código 2FA inválido' })
  async verifyTwoFactor(
    @Body() body: { token: string },
    @Request() req,
  ): Promise<{ message: string }> {
    return this.authService.verifyTwoFactor(req.user.sub, body.token);
  }
}
