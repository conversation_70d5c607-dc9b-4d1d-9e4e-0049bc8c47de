import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as amqp from 'amqplib';

/**
 * Serviço RabbitMQ para sistema de filas conforme T-047 do backlog
 * - Processamento assíncrono de e-mails
 * - Notificações em tempo real
 * - Processamento de relatórios
 * - Integração com sistemas externos
 */
@Injectable()
export class RabbitMQService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RabbitMQService.name);
  private connection: any = null;
  private channel: any = null;
  private readonly exchanges = {
    EMAIL: 'email.exchange',
    NOTIFICATIONS: 'notifications.exchange',
    REPORTS: 'reports.exchange',
    INTEGRATIONS: 'integrations.exchange',
  };
  private readonly queues = {
    EMAIL_SEND: 'email.send',
    EMAIL_OTP: 'email.otp',
    NOTIFICATION_PUSH: 'notification.push',
    NOTIFICATION_SMS: 'notification.sms',
    REPORT_GENERATE: 'report.generate',
    REPORT_EXPORT: 'report.export',
    INTEGRATION_SYNC: 'integration.sync',
    INTEGRATION_WEBHOOK: 'integration.webhook',
  };

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    await this.connect();
    await this.setupExchangesAndQueues();
    this.logger.log('RabbitMQ service initialized successfully');
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  private async connect(): Promise<void> {
    try {
      const rabbitmqUrl = this.configService.get('RABBITMQ_URL', 'amqp://rabbitmq:rabbitmq123@rabbitmq:5672');
      this.connection = await amqp.connect(rabbitmqUrl);
      this.channel = await this.connection.createChannel();
      
      // Configure channel for reliability
      await this.channel.prefetch(10); // Process max 10 messages at a time
      
      this.logger.log('Connected to RabbitMQ successfully');
    } catch (error) {
      this.logger.error('Failed to connect to RabbitMQ:', error);
      throw error;
    }
  }

  private async disconnect(): Promise<void> {
    try {
      if (this.channel) {
        await this.channel.close();
        this.channel = null;
      }
      if (this.connection) {
        await this.connection.close();
        this.connection = null;
      }
      this.logger.log('Disconnected from RabbitMQ');
    } catch (error) {
      this.logger.error('Error disconnecting from RabbitMQ:', error);
    }
  }

  private async setupExchangesAndQueues(): Promise<void> {
    try {
      if (!this.channel) {
        throw new Error('RabbitMQ channel not available');
      }

      // Setup exchanges
      for (const exchange of Object.values(this.exchanges)) {
        await this.channel.assertExchange(exchange, 'topic', { durable: true });
      }

      // Setup queues and bindings
      await this.setupEmailQueues();
      await this.setupNotificationQueues();
      await this.setupReportQueues();
      await this.setupIntegrationQueues();

      this.logger.log('All exchanges and queues setup successfully');
    } catch (error) {
      this.logger.error('Failed to setup exchanges and queues:', error);
      throw error;
    }
  }

  private async setupEmailQueues(): Promise<void> {
    if (!this.channel) return;

    // Email sending queue
    await this.channel.assertQueue(this.queues.EMAIL_SEND, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.email',
        'x-message-ttl': 300000, // 5 minutes TTL
      }
    });
    await this.channel.bindQueue(this.queues.EMAIL_SEND, this.exchanges.EMAIL, 'email.send.*');

    // OTP email queue (high priority)
    await this.channel.assertQueue(this.queues.EMAIL_OTP, {
      durable: true,
      arguments: {
        'x-max-priority': 10,
        'x-dead-letter-exchange': 'dlx.email',
        'x-message-ttl': 60000, // 1 minute TTL for OTP
      }
    });
    await this.channel.bindQueue(this.queues.EMAIL_OTP, this.exchanges.EMAIL, 'email.otp.*');
  }

  private async setupNotificationQueues(): Promise<void> {
    if (!this.channel) return;

    // Push notifications
    await this.channel.assertQueue(this.queues.NOTIFICATION_PUSH, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.notifications',
      }
    });
    await this.channel.bindQueue(this.queues.NOTIFICATION_PUSH, this.exchanges.NOTIFICATIONS, 'notification.push.*');

    // SMS notifications
    await this.channel.assertQueue(this.queues.NOTIFICATION_SMS, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.notifications',
        'x-max-priority': 5,
      }
    });
    await this.channel.bindQueue(this.queues.NOTIFICATION_SMS, this.exchanges.NOTIFICATIONS, 'notification.sms.*');
  }

  private async setupReportQueues(): Promise<void> {
    if (!this.channel) return;

    // Report generation (long-running tasks)
    await this.channel.assertQueue(this.queues.REPORT_GENERATE, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.reports',
        'x-message-ttl': 1800000, // 30 minutes TTL
      }
    });
    await this.channel.bindQueue(this.queues.REPORT_GENERATE, this.exchanges.REPORTS, 'report.generate.*');

    // Report export
    await this.channel.assertQueue(this.queues.REPORT_EXPORT, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.reports',
      }
    });
    await this.channel.bindQueue(this.queues.REPORT_EXPORT, this.exchanges.REPORTS, 'report.export.*');
  }

  private async setupIntegrationQueues(): Promise<void> {
    if (!this.channel) return;

    // Integration sync
    await this.channel.assertQueue(this.queues.INTEGRATION_SYNC, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.integrations',
        'x-message-ttl': 600000, // 10 minutes TTL
      }
    });
    await this.channel.bindQueue(this.queues.INTEGRATION_SYNC, this.exchanges.INTEGRATIONS, 'integration.sync.*');

    // Webhook processing
    await this.channel.assertQueue(this.queues.INTEGRATION_WEBHOOK, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'dlx.integrations',
        'x-max-priority': 8,
      }
    });
    await this.channel.bindQueue(this.queues.INTEGRATION_WEBHOOK, this.exchanges.INTEGRATIONS, 'integration.webhook.*');
  }

  /**
   * Publish message to queue
   */
  async publishMessage(exchange: string, routingKey: string, message: any, options: any = {}): Promise<boolean> {
    try {
      if (!this.channel) {
        throw new Error('RabbitMQ channel not available');
      }

      const messageBuffer = Buffer.from(JSON.stringify(message));
      const publishOptions = {
        persistent: true,
        timestamp: Date.now(),
        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        ...options,
      };

      const result = this.channel.publish(exchange, routingKey, messageBuffer, publishOptions);
      
      if (result) {
        this.logger.debug(`Message published to ${exchange}/${routingKey}`);
      } else {
        this.logger.warn(`Failed to publish message to ${exchange}/${routingKey}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error(`Error publishing message to ${exchange}/${routingKey}:`, error);
      return false;
    }
  }

  /**
   * Send OTP email (high priority)
   */
  async sendOtpEmail(email: string, code: string, expiresIn: number): Promise<boolean> {
    const message = {
      to: email,
      subject: 'Código de Verificação - Retail Media',
      template: 'otp',
      data: {
        code,
        expiresIn,
        timestamp: new Date().toISOString(),
      },
    };

    return this.publishMessage(
      this.exchanges.EMAIL,
      'email.otp.send',
      message,
      { priority: 10 }
    );
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(email: string, name: string): Promise<boolean> {
    const message = {
      to: email,
      subject: 'Bem-vindo ao Retail Media',
      template: 'welcome',
      data: {
        name,
        timestamp: new Date().toISOString(),
      },
    };

    return this.publishMessage(
      this.exchanges.EMAIL,
      'email.send.welcome',
      message
    );
  }

  /**
   * Send push notification
   */
  async sendPushNotification(userId: string, title: string, body: string, data: any = {}): Promise<boolean> {
    const message = {
      userId,
      title,
      body,
      data,
      timestamp: new Date().toISOString(),
    };

    return this.publishMessage(
      this.exchanges.NOTIFICATIONS,
      'notification.push.send',
      message
    );
  }

  /**
   * Generate report (async)
   */
  async generateReport(reportType: string, userId: string, parameters: any): Promise<boolean> {
    const message = {
      reportType,
      userId,
      parameters,
      requestId: `report-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
    };

    return this.publishMessage(
      this.exchanges.REPORTS,
      `report.generate.${reportType}`,
      message
    );
  }

  /**
   * Sync with external system
   */
  async syncWithExternalSystem(systemType: string, operation: string, data: any): Promise<boolean> {
    const message = {
      systemType,
      operation,
      data,
      syncId: `sync-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
    };

    return this.publishMessage(
      this.exchanges.INTEGRATIONS,
      `integration.sync.${systemType}`,
      message
    );
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; connection: boolean; channel: boolean }> {
    try {
      const connectionOk = !!this.connection;
      const channelOk = !!this.channel;

      return {
        status: connectionOk && channelOk ? 'healthy' : 'unhealthy',
        connection: connectionOk,
        channel: channelOk,
      };
    } catch (error) {
      this.logger.error('RabbitMQ health check failed:', error);
      return {
        status: 'unhealthy',
        connection: false,
        channel: false,
      };
    }
  }

  /**
   * Get queue stats
   */
  async getQueueStats(): Promise<any> {
    try {
      if (!this.channel) {
        throw new Error('RabbitMQ channel not available');
      }

      const stats = {};
      for (const [name, queue] of Object.entries(this.queues)) {
        try {
          const queueInfo = await this.channel.checkQueue(queue);
          stats[name] = {
            queue,
            messageCount: queueInfo.messageCount,
            consumerCount: queueInfo.consumerCount,
          };
        } catch (error) {
          stats[name] = { queue, error: error.message };
        }
      }
      return stats;
    } catch (error) {
      this.logger.error('Failed to get queue stats:', error);
      return { error: error.message };
    }
  }
}
