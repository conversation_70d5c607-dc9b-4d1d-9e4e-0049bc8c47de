@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Primary colors - Blue theme */
  --primary: #2563eb; /* blue-600 */
  --primary-foreground: #ffffff;

  /* Secondary colors */
  --secondary: #f1f5f9; /* slate-100 */
  --secondary-foreground: #0f172a; /* slate-900 */

  /* Destructive colors */
  --destructive: #dc2626; /* red-600 */
  --destructive-foreground: #ffffff;

  /* Accent colors */
  --accent: #f1f5f9; /* slate-100 */
  --accent-foreground: #0f172a; /* slate-900 */

  /* Border and input colors */
  --border: #e2e8f0; /* slate-200 */
  --input: #e2e8f0; /* slate-200 */
  --ring: #2563eb; /* blue-600 */

  /* Muted colors */
  --muted: #f8fafc; /* slate-50 */
  --muted-foreground: #64748b; /* slate-500 */

  /* Card colors */
  --card: #ffffff;
  --card-foreground: #0f172a; /* slate-900 */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;

    /* Primary colors remain the same in dark mode */
    --primary: #3b82f6; /* blue-500 - slightly lighter for dark mode */
    --primary-foreground: #ffffff;

    /* Secondary colors for dark mode */
    --secondary: #1e293b; /* slate-800 */
    --secondary-foreground: #f1f5f9; /* slate-100 */

    /* Card colors for dark mode */
    --card: #1e293b; /* slate-800 */
    --card-foreground: #f1f5f9; /* slate-100 */

    /* Border and input colors for dark mode */
    --border: #334155; /* slate-700 */
    --input: #334155; /* slate-700 */

    /* Muted colors for dark mode */
    --muted: #1e293b; /* slate-800 */
    --muted-foreground: #94a3b8; /* slate-400 */
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Ocultar ícone de desenvolvimento do Next.js */
#__next-build-watcher {
  display: none !important;
}

/* Ocultar qualquer overlay de desenvolvimento */
[data-nextjs-dialog-overlay],
[data-nextjs-toast],
.__next-dev-overlay,
nextjs-portal {
  display: none !important;
}

/* Ocultar botão flutuante do Next.js */
button[data-nextjs-data-runtime-error-collapsed-action] {
  display: none !important;
}

/* Melhorar contraste de textos */
.text-gray-400 {
  color: rgb(107 114 128) !important; /* gray-500 */
}

.text-gray-500 {
  color: rgb(75 85 99) !important; /* gray-600 */
}

.text-gray-600 {
  color: rgb(55 65 81) !important; /* gray-700 */
}

/* Garantir que placeholders sejam legíveis */
::placeholder {
  color: rgb(107 114 128) !important; /* gray-500 */
  opacity: 1;
}

/* Melhorar contraste em inputs */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
textarea,
select {
  color: rgb(17 24 39) !important; /* gray-900 */
}

/* Garantir que labels sejam bem visíveis */
label {
  color: rgb(31 41 55) !important; /* gray-800 */
}

/* Melhorar contraste em textos pequenos */
.text-xs {
  color: rgb(55 65 81) !important; /* gray-700 */
}

.text-sm {
  color: rgb(55 65 81) !important; /* gray-700 */
}

/* Garantir que textos de descrição sejam legíveis */
p:not(.text-white):not(.text-blue-600):not(.text-red-600):not(.text-green-600):not(.text-yellow-600):not(.text-purple-600):not(.text-orange-600) {
  color: rgb(55 65 81) !important; /* gray-700 */
}

/* Garantir que botões com fundo azul tenham texto e ícones brancos */
.bg-blue-600,
.bg-blue-700,
.bg-primary,
button[class*="bg-blue-6"],
button[class*="bg-primary"] {
  color: white !important;
}

.bg-blue-600 svg,
.bg-blue-700 svg,
.bg-primary svg,
button[class*="bg-blue-6"] svg,
button[class*="bg-primary"] svg {
  color: white !important;
}

/* Garantir que botões primários do shadcn/ui tenham cores corretas */
button[data-variant="default"],
.bg-primary {
  background-color: var(--primary) !important;
  color: var(--primary-foreground) !important;
}

button[data-variant="default"] svg,
.bg-primary svg {
  color: var(--primary-foreground) !important;
}

/* Garantir que ícones dentro de botões azuis sejam brancos */
button.bg-blue-600 > svg,
button.bg-blue-700 > svg,
button.bg-primary > svg,
.bg-blue-600 > svg,
.bg-blue-700 > svg,
.bg-primary > svg {
  color: white !important;
  fill: white !important;
}

/* Garantir que spans dentro de botões azuis sejam brancos */
button.bg-blue-600 > span,
button.bg-blue-700 > span,
button.bg-primary > span,
.bg-blue-600 > span,
.bg-blue-700 > span,
.bg-primary > span {
  color: white !important;
}
