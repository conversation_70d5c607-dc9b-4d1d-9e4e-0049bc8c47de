@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Ocultar ícone de desenvolvimento do Next.js */
#__next-build-watcher {
  display: none !important;
}

/* Ocultar qualquer overlay de desenvolvimento */
[data-nextjs-dialog-overlay],
[data-nextjs-toast],
.__next-dev-overlay,
nextjs-portal {
  display: none !important;
}

/* Ocultar botão flutuante do Next.js */
button[data-nextjs-data-runtime-error-collapsed-action] {
  display: none !important;
}

/* Melhorar contraste de textos */
.text-gray-400 {
  color: rgb(107 114 128) !important; /* gray-500 */
}

.text-gray-500 {
  color: rgb(75 85 99) !important; /* gray-600 */
}

.text-gray-600 {
  color: rgb(55 65 81) !important; /* gray-700 */
}

/* Garantir que placeholders sejam legíveis */
::placeholder {
  color: rgb(107 114 128) !important; /* gray-500 */
  opacity: 1;
}

/* Melhorar contraste em inputs */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
textarea,
select {
  color: rgb(17 24 39) !important; /* gray-900 */
}

/* Garantir que labels sejam bem visíveis */
label {
  color: rgb(31 41 55) !important; /* gray-800 */
}

/* Melhorar contraste em textos pequenos */
.text-xs {
  color: rgb(55 65 81) !important; /* gray-700 */
}

.text-sm {
  color: rgb(55 65 81) !important; /* gray-700 */
}


